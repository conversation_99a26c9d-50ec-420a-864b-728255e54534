<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倍數快跑遊戲 - 數學學習樂園</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }

        .game-area {
            position: relative;
            width: 100%;
            height: 500px;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
            border: 3px solid #fff;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .road {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: linear-gradient(to right, #666 0%, #888 50%, #666 100%);
            border-top: 4px solid #fff;
        }

        .road-lines {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 4px;
            background: repeating-linear-gradient(
                to right,
                #fff 0px,
                #fff 30px,
                transparent 30px,
                transparent 60px
            );
            transform: translateY(-50%);
        }

        .character {
            position: absolute;
            bottom: 130px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 60px;
            background: #FF6B9D;
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            border: 3px solid #fff;
            transition: left 0.1s ease;
            z-index: 10;
        }

        .character::before {
            content: '😊';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
        }

        .character.running {
            animation: bounce 0.3s infinite alternate;
        }

        @keyframes bounce {
            0% { transform: translateX(-50%) translateY(0px); }
            100% { transform: translateX(-50%) translateY(-5px); }
        }

        .item {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            animation: fall 2s linear forwards;
        }

        .item.correct {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .item.incorrect {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        @keyframes fall {
            0% {
                transform: translateY(-50px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            100% {
                transform: translateY(550px);
                opacity: 1;
            }
        }

        .score-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
            padding: 15px 25px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .score-item {
            text-align: center;
        }

        .score-item h3 {
            margin: 0 0 5px 0;
            color: #666;
            font-size: 14px;
            font-weight: 700;
        }

        .score-item .value {
            font-size: 24px;
            font-weight: 900;
            color: var(--primary);
        }

        .target-number {
            font-size: 28px;
            color: var(--secondary);
            font-weight: 900;
        }

        .controls {
            margin: 20px 0;
        }

        .control-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 145, 255, 0.4);
        }

        .control-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 100;
        }

        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 700;
            animation: popInCute 0.3s ease;
        }

        .speed-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 10px;
            font-weight: 700;
            color: var(--primary);
        }

        .instructions {
            background: #fff;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .instructions h3 {
            color: var(--primary);
            margin-top: 0;
        }

        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .instructions li {
            margin: 8px 0;
            color: #666;
        }

        .level-selector {
            margin: 20px 0;
        }

        .level-btn {
            background: var(--accent);
            color: #593f00;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 700;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .level-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 208, 107, 0.4);
        }

        .level-btn.active {
            background: var(--secondary);
            color: white;
        }
    </style>
</head>
<body>
    <div class="cute-container">
        <div class="game-container">
            <h1>🏃‍♂️ 倍數快跑遊戲</h1>
            
            <div class="instructions">
                <h3>🎯 遊戲規則</h3>
                <ul>
                    <li>小角色會在道路上奔跑</li>
                    <li>只能收集「目標數字」的倍數道具</li>
                    <li>收集正確道具得 10 分</li>
                    <li>收集錯誤道具扣 5 分</li>
                    <li>使用方向鍵左右移動角色</li>
                </ul>
            </div>

            <div class="level-selector">
                <h3>選擇難度</h3>
                <button class="level-btn active" data-level="easy">簡單 (2-5倍)</button>
                <button class="level-btn" data-level="medium">中等 (3-7倍)</button>
                <button class="level-btn" data-level="hard">困難 (5-10倍)</button>
            </div>

            <div class="score-panel">
                <div class="score-item">
                    <h3>分數</h3>
                    <div class="value" id="score">0</div>
                </div>
                <div class="score-item">
                    <h3>目標數字</h3>
                    <div class="target-number" id="targetNumber">3</div>
                </div>
                <div class="score-item">
                    <h3>生命值</h3>
                    <div class="value" id="lives">❤️❤️❤️</div>
                </div>
            </div>

            <div class="game-area" id="gameArea">
                <div class="speed-indicator">
                    速度: <span id="speedLevel">1</span>
                </div>
                <div class="road">
                    <div class="road-lines"></div>
                </div>
                <div class="character" id="character">😊</div>
            </div>

            <div class="controls">
                <button class="control-btn" id="startBtn">開始遊戲</button>
                <button class="control-btn" id="pauseBtn" disabled>暫停</button>
                <button class="control-btn" id="resetBtn">重新開始</button>
            </div>

            <div class="high-score">
                最高分數: <span id="highScore">0</span>
            </div>
        </div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';

        class MultipleRunnerGame {
            constructor() {
                this.gameArea = document.getElementById('gameArea');
                this.character = document.getElementById('character');
                this.scoreElement = document.getElementById('score');
                this.targetNumberElement = document.getElementById('targetNumber');
                this.livesElement = document.getElementById('lives');
                this.speedElement = document.getElementById('speedLevel');
                this.highScoreElement = document.getElementById('highScore');
                
                this.score = 0;
                this.lives = 3;
                this.targetNumber = 3;
                this.gameSpeed = 1;
                this.isRunning = false;
                this.isPaused = false;
                this.characterPosition = 50; // 百分比
                this.itemSpawnTimer = 0;
                this.speedIncreaseTimer = 0;
                this.level = 'easy';
                
                this.setupEventListeners();
                this.loadHighScore();
                this.updateDisplay();
            }

            setupEventListeners() {
                document.getElementById('startBtn').addEventListener('click', () => this.startGame());
                document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                
                // 方向鍵控制
                document.addEventListener('keydown', (e) => {
                    if (!this.isRunning || this.isPaused) return;
                    
                    if (e.key === 'ArrowLeft') {
                        this.moveCharacter(-10);
                        e.preventDefault();
                    } else if (e.key === 'ArrowRight') {
                        this.moveCharacter(10);
                        e.preventDefault();
                    }
                });

                // 難度選擇
                document.querySelectorAll('.level-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.level-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.level = e.target.dataset.level;
                        this.resetGame();
                    });
                });
            }

            loadHighScore() {
                const highScore = getHighScore('multipleRunner');
                this.highScoreElement.textContent = highScore;
            }

            setDifficulty() {
                const difficulties = {
                    easy: { target: [2, 3, 4, 5], speed: 1.2 },
                    medium: { target: [3, 4, 5, 6, 7], speed: 1.5 },
                    hard: { target: [5, 6, 7, 8, 9, 10], speed: 1.8 }
                };
                
                const config = difficulties[this.level];
                this.targetNumber = config.target[Math.floor(Math.random() * config.target.length)];
                this.baseSpeed = config.speed;
                this.gameSpeed = this.baseSpeed;
            }

            startGame() {
                if (this.isRunning) return;
                
                this.setDifficulty();
                this.isRunning = true;
                this.isPaused = false;
                this.character.classList.add('running');
                
                document.getElementById('startBtn').disabled = true;
                document.getElementById('pauseBtn').disabled = false;
                
                this.gameLoop();
                playSound('click');
            }

            togglePause() {
                if (!this.isRunning) return;
                
                this.isPaused = !this.isPaused;
                
                if (this.isPaused) {
                    this.character.classList.remove('running');
                    document.getElementById('pauseBtn').textContent = '繼續';
                } else {
                    this.character.classList.add('running');
                    document.getElementById('pauseBtn').textContent = '暫停';
                    this.gameLoop();
                }
                
                playSound('click');
            }

            resetGame() {
                this.isRunning = false;
                this.isPaused = false;
                this.score = 0;
                this.lives = 3;
                this.gameSpeed = this.baseSpeed || 1;
                this.itemSpawnTimer = 0;
                this.speedIncreaseTimer = 0;
                this.characterPosition = 50;
                
                this.character.classList.remove('running');
                this.character.style.left = '50%';
                
                // 清除所有道具
                document.querySelectorAll('.item').forEach(item => item.remove());
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('pauseBtn').disabled = true;
                document.getElementById('pauseBtn').textContent = '暫停';
                
                this.setDifficulty();
                this.updateDisplay();
                playSound('click');
            }

            moveCharacter(direction) {
                this.characterPosition += direction;
                this.characterPosition = Math.max(10, Math.min(90, this.characterPosition));
                this.character.style.left = this.characterPosition + '%';
            }

            spawnItem() {
                const item = document.createElement('div');
                item.className = 'item';
                
                // 生成數字
                const isCorrect = Math.random() < 0.6; // 60% 正確率
                let number;
                
                if (isCorrect) {
                    // 生成目標數字的倍數
                    const multiplier = Math.floor(Math.random() * 5) + 1;
                    number = this.targetNumber * multiplier;
                    item.classList.add('correct');
                } else {
                    // 生成非倍數
                    do {
                        number = Math.floor(Math.random() * 50) + 1;
                    } while (number % this.targetNumber === 0);
                    item.classList.add('incorrect');
                }
                
                item.textContent = number;
                item.dataset.number = number;
                item.dataset.isCorrect = isCorrect;
                
                // 隨機位置
                const leftPosition = Math.random() * 80 + 10; // 10% 到 90%
                item.style.left = leftPosition + '%';
                item.style.top = '-50px';
                
                this.gameArea.appendChild(item);
                
                // 設置碰撞檢測
                setTimeout(() => {
                    if (item.parentNode) {
                        this.checkCollision(item);
                        item.remove();
                    }
                }, 2000);
            }

            checkCollision(item) {
                const itemRect = item.getBoundingClientRect();
                const characterRect = this.character.getBoundingClientRect();
                
                if (itemRect.left < characterRect.right &&
                    itemRect.right > characterRect.left &&
                    itemRect.top < characterRect.bottom &&
                    itemRect.bottom > characterRect.top) {
                    
                    const isCorrect = item.dataset.isCorrect === 'true';
                    
                    if (isCorrect) {
                        this.score += 10;
                        playSound('success');
                        this.showMessage(`+10 分！收集了 ${item.dataset.number}`, 'success');
                    } else {
                        this.score = Math.max(0, this.score - 5);
                        this.lives--;
                        playSound('error');
                        this.showMessage(`-5 分！${item.dataset.number} 不是 ${this.targetNumber} 的倍數`, 'error');
                        
                        if (this.lives <= 0) {
                            this.gameOver();
                            return;
                        }
                    }
                    
                    item.remove();
                    this.updateDisplay();
                    
                    // 增加速度
                    this.speedIncreaseTimer++;
                    if (this.speedIncreaseTimer % 10 === 0) {
                        this.gameSpeed += 0.1;
                        this.updateSpeedDisplay();
                    }
                }
            }

            showMessage(text, type) {
                const message = document.createElement('div');
                message.className = `message ${type}`;
                message.textContent = text;
                message.style.position = 'absolute';
                message.style.top = '20px';
                message.style.left = '50%';
                message.style.transform = 'translateX(-50%)';
                message.style.zIndex = '1000';
                
                this.gameArea.appendChild(message);
                
                setTimeout(() => {
                    if (message.parentNode) {
                        message.remove();
                    }
                }, 2000);
            }

            updateDisplay() {
                this.scoreElement.textContent = this.score;
                this.targetNumberElement.textContent = this.targetNumber;
                this.livesElement.textContent = '❤️'.repeat(this.lives);
            }

            updateSpeedDisplay() {
                this.speedElement.textContent = Math.round(this.gameSpeed * 10) / 10;
            }

            gameLoop() {
                if (!this.isRunning || this.isPaused) return;
                
                this.itemSpawnTimer++;
                
                // 生成道具
                const spawnInterval = Math.max(30, 60 - this.gameSpeed * 10);
                if (this.itemSpawnTimer >= spawnInterval) {
                    this.spawnItem();
                    this.itemSpawnTimer = 0;
                }
                
                // 移動現有道具
                document.querySelectorAll('.item').forEach(item => {
                    const currentTop = parseFloat(item.style.top) || -50;
                    const newTop = currentTop + this.gameSpeed * 2;
                    item.style.top = newTop + 'px';
                });
                
                requestAnimationFrame(() => this.gameLoop());
            }

            gameOver() {
                this.isRunning = false;
                this.character.classList.remove('running');
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('pauseBtn').disabled = true;
                
                // 檢查最高分
                const currentHighScore = getHighScore('multipleRunner');
                if (this.score > currentHighScore) {
                    setHighScore('multipleRunner', this.score);
                    this.highScoreElement.textContent = this.score;
                    celebrateConfetti();
                    this.showGameOverMessage(`🎉 新紀錄！${this.score} 分！`);
                } else {
                    this.showGameOverMessage(`遊戲結束！得分: ${this.score}`);
                }
                
                playSound('success');
            }

            showGameOverMessage(message) {
                const gameOverDiv = document.createElement('div');
                gameOverDiv.className = 'game-over';
                gameOverDiv.innerHTML = `
                    <h2>${message}</h2>
                    <p>目標數字: ${this.targetNumber}</p>
                    <button class="control-btn" onclick="this.parentElement.remove(); game.resetGame();">
                        再玩一次
                    </button>
                `;
                
                this.gameArea.appendChild(gameOverDiv);
            }
        }

        // 初始化遊戲
        const game = new MultipleRunnerGame();
    </script>
</body>
</html>
