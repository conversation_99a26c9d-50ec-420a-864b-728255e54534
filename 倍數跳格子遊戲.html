<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倍數跳格子遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        select, button {
            font-size: 1.1em;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #c0392b;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 5px;
            margin: 30px auto;
            max-width: 500px;
        }
        .cell {
            width: 40px;
            height: 40px;
            border: 2px solid #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s;
            background: #ecf0f1;
        }
        .cell:hover {
            transform: scale(1.1);
        }
        .cell.selected {
            background: #2ecc71;
            color: white;
            animation: bounce 0.5s ease;
        }
        .cell.wrong {
            background: #e74c3c;
            color: white;
            animation: shake 0.5s ease;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🦘 倍數跳格子遊戲</h1>
        
        <div class="controls">
            <label>選擇倍數：</label>
            <select id="multiplierSelect">
                <option value="2">2的倍數</option>
                <option value="3">3的倍數</option>
                <option value="4">4的倍數</option>
                <option value="5">5的倍數</option>
                <option value="6">6的倍數</option>
            </select>
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">點擊正確的倍數格子！</div>
        
        <div class="grid" id="grid"></div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let currentMultiplier = 2;
        let score = 0;
        let gameActive = true;
        const HS_KEY = 'hs-multiple-grid';

        function createGrid() {
            const grid = document.getElementById('grid');
            grid.innerHTML = '';
            
            for (let i = 1; i <= 100; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.textContent = i;
                cell.onclick = () => selectCell(i, cell);
                grid.appendChild(cell);
            }
        }

        function selectCell(number, cellElement) {
            if (!gameActive) return;
            
            if (number % currentMultiplier === 0) {
                cellElement.classList.add('selected');
                score += 10;
                showMessage(`正確！${number} 是 ${currentMultiplier} 的倍數！`, 'success');
                playSound('success');
                updateScore();
            } else {
                cellElement.classList.add('wrong');
                showMessage(`錯誤！${number} 不是 ${currentMultiplier} 的倍數！`, 'error');
                playSound('error');
                setTimeout(() => {
                    cellElement.classList.remove('wrong');
                }, 1000);
            }
        }

        function newGame() {
            gameActive = true;
            score = 0;
            currentMultiplier = parseInt(document.getElementById('multiplierSelect').value);
            
            createGrid();
            updateScore();
            showMessage(`開始尋找 ${currentMultiplier} 的倍數！`, 'success');
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            document.getElementById('score').innerHTML = `得分: ${score} <span class="high-score">(最高分：${high})</span>`;
            setHighScore(HS_KEY, score);
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        // 初始化遊戲
        createGrid();
    </script>
</body>
</html>
