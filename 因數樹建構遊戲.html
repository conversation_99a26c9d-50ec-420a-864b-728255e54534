<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數樹建構遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #7d3c98;
        }
        .target-number {
            font-size: 2.5em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }
        .factor-options {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        .factor-option {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            margin: 10px;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 60px;
        }
        .factor-option:hover {
            background: #2980b9;
            transform: scale(1.1);
        }
        .factor-option.used {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .tree-container {
            margin: 30px 0;
            min-height: 200px;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }
        .tree-node {
            background: #2ecc71;
            color: white;
            padding: 15px 20px;
            margin: 10px;
            border-radius: 50%;
            font-size: 1.2em;
            font-weight: bold;
            min-width: 60px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: popIn 0.5s ease;
        }
        @keyframes popIn {
            0% { transform: scale(0); }
            100% { transform: scale(1); }
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🌳 因數樹建構遊戲</h1>
        
        <div class="controls">
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="target-number" id="targetNumber">24</div>
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">點擊因數來建構因數樹！</div>
        
        <div class="factor-options" id="factorOptions"></div>
        <div class="tree-container" id="treeContainer"></div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let targetNumber = 24;
        let currentNumber = 24;
        let usedFactors = [];
        let score = 0;
        let gameActive = true;
        const HS_KEY = 'hs-factor-tree';

        const numbers = [12, 18, 24, 30, 36, 42, 48, 56, 60, 72, 84, 96];

        function generateRandomNumber() {
            return numbers[Math.floor(Math.random() * numbers.length)];
        }

        function getFactors(num) {
            const factors = [];
            for (let i = 2; i < num; i++) {
                if (num % i === 0) {
                    factors.push(i);
                }
            }
            return factors;
        }

        function createFactorOptions() {
            const factors = getFactors(currentNumber);
            const optionsDiv = document.getElementById('factorOptions');
            optionsDiv.innerHTML = '';
            
            factors.forEach(factor => {
                const div = document.createElement('div');
                div.className = 'factor-option';
                div.textContent = factor;
                div.onclick = () => selectFactor(factor);
                optionsDiv.appendChild(div);
            });
        }

        function selectFactor(factor) {
            if (!gameActive || usedFactors.includes(factor)) return;
            
            usedFactors.push(factor);
            currentNumber = currentNumber / factor;
            score += 10;
            
            updateTree();
            updateScore();
            
            if (currentNumber === 1) {
                showMessage('恭喜！因數樹建構完成！', 'success');
                playSound('success');
                celebrateConfetti(90);
                setTimeout(newGame, 2000);
            } else if (getFactors(currentNumber).length === 0) {
                showMessage('無法繼續分解！重新開始！', 'error');
                playSound('error');
                setTimeout(newGame, 2000);
            } else {
                createFactorOptions();
                showMessage(`選擇 ${currentNumber} 的因數！`, 'success');
            }
        }

        function updateTree() {
            const treeDiv = document.getElementById('treeContainer');
            treeDiv.innerHTML = '';
            
            // 顯示原始數字
            const rootDiv = document.createElement('div');
            rootDiv.className = 'tree-node';
            rootDiv.textContent = targetNumber;
            treeDiv.appendChild(rootDiv);
            
            // 顯示已使用的因數
            usedFactors.forEach(factor => {
                const factorDiv = document.createElement('div');
                factorDiv.className = 'tree-node';
                factorDiv.textContent = factor;
                treeDiv.appendChild(factorDiv);
            });
            
            // 顯示當前數字
            if (currentNumber !== 1) {
                const currentDiv = document.createElement('div');
                currentDiv.className = 'tree-node';
                currentDiv.textContent = currentNumber;
                currentDiv.style.background = '#e74c3c';
                treeDiv.appendChild(currentDiv);
            }
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `得分: ${score} <span class=\"high-score\">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            targetNumber = generateRandomNumber();
            currentNumber = targetNumber;
            usedFactors = [];
            score = 0;
            
            document.getElementById('targetNumber').textContent = targetNumber;
            updateScore();
            createFactorOptions();
            updateTree();
            showMessage('點擊因數來建構因數樹！', 'success');
        }

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
