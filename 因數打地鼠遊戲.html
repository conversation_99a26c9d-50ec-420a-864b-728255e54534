<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數打地鼠遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        select, button {
            font-size: 1.1em;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #c0392b;
        }
        .target-number {
            font-size: 3em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
            animation: pulse 1s ease infinite;
        }
        .timer {
            font-size: 1.5em;
            color: #f39c12;
            margin: 20px 0;
        }
        .moles-container {
            position: relative;
            width: 100%;
            height: 450px;
            background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
            border-radius: 20px;
            margin: 30px 0;
            overflow: hidden;
            border: 4px solid #654321;
            min-height: 450px;
        }
        .mole {
            position: absolute;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.5em;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: popUp 0.5s ease;
        }
        .mole:hover {
            transform: scale(1.1);
        }
        .mole.hit {
            background: #2ecc71;
            animation: hit 0.3s ease;
        }
        .mole.miss {
            background: #e74c3c;
            animation: miss 0.3s ease;
        }
        @keyframes popUp {
            0% { transform: scale(0) translateY(50px); opacity: 0; }
            50% { transform: scale(1.1) translateY(-10px); opacity: 1; }
            100% { transform: scale(1) translateY(0); opacity: 1; }
        }
        @keyframes hit {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        @keyframes miss {
            0% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .game-over {
            background: #fff3cd;
            color: #856404;
            border: 3px solid #ffc107;
            animation: celebrate 1s ease infinite;
        }
        @keyframes celebrate {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .mole-hole {
            position: absolute;
            width: 80px;
            height: 80px;
            background: #654321;
            border-radius: 50%;
            border: 3px solid #8B4513;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.3);
        }
        
        /* 響應式設計 */
        @media (max-width: 768px) {
            .moles-container {
                height: 400px;
                min-height: 400px;
            }
            .mole-hole {
                width: 60px;
                height: 60px;
            }
            .mole {
                width: 60px;
                height: 60px;
                font-size: 1.2em;
            }
        }
        
        @media (max-width: 480px) {
            .moles-container {
                height: 350px;
                min-height: 350px;
            }
            .mole-hole {
                width: 50px;
                height: 50px;
            }
            .mole {
                width: 50px;
                height: 50px;
                font-size: 1em;
            }
        }
        .instructions {
            background: #e8f4fd;
            border: 2px solid #3498db;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .instructions h3 {
            color: #2980b9;
            margin-top: 0;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
            color: #2c3e50;
        }
        .confetti-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            pointer-events: none;
            z-index: 1000;
        }
        .confetti-piece {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            animation: confetti-fall linear forwards;
        }
        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) translateX(var(--cx-end, 0)) rotate(720deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🐹 因數打地鼠遊戲</h1>
        
        <div class="instructions">
            <h3>🎯 遊戲說明</h3>
            <ul>
                <li>螢幕中央會顯示一個目標數字</li>
                <li>地鼠會從洞裡冒出，身上有不同數字</li>
                <li>點擊「是目標數字因數」的地鼠獲得10分</li>
                <li>點擊「不是因數」的地鼠扣5分</li>
                <li>在時間限制內獲得最高分數！</li>
            </ul>
        </div>
        
        <div class="controls">
            <label>選擇難度：</label>
            <select id="difficultySelect">
                <option value="easy">簡單 (30秒)</option>
                <option value="medium">中等 (20秒)</option>
                <option value="hard">困難 (15秒)</option>
            </select>
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="target-number" id="targetNumber">12</div>
        <div class="timer" id="timer">時間: 30秒</div>
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">準備開始！點擊新遊戲！</div>
        
        <div class="moles-container" id="molesContainer">
            <!-- 地鼠洞位置 - 使用響應式佈局 -->
            <!-- 第一行：5個洞 -->
            <div class="mole-hole" style="top: 40px; left: 8%;"></div>
            <div class="mole-hole" style="top: 40px; left: 28%;"></div>
            <div class="mole-hole" style="top: 40px; left: 48%;"></div>
            <div class="mole-hole" style="top: 40px; left: 68%;"></div>
            <div class="mole-hole" style="top: 40px; left: 88%;"></div>
            
            <!-- 第二行：4個洞 -->
            <div class="mole-hole" style="top: 140px; left: 18%;"></div>
            <div class="mole-hole" style="top: 140px; left: 38%;"></div>
            <div class="mole-hole" style="top: 140px; left: 58%;"></div>
            <div class="mole-hole" style="top: 140px; left: 78%;"></div>
            
            <!-- 第三行：5個洞 -->
            <div class="mole-hole" style="top: 240px; left: 8%;"></div>
            <div class="mole-hole" style="top: 240px; left: 28%;"></div>
            <div class="mole-hole" style="top: 240px; left: 48%;"></div>
            <div class="mole-hole" style="top: 240px; left: 68%;"></div>
            <div class="mole-hole" style="top: 240px; left: 88%;"></div>
            
            <!-- 第四行：4個洞 -->
            <div class="mole-hole" style="top: 340px; left: 18%;"></div>
            <div class="mole-hole" style="top: 340px; left: 38%;"></div>
            <div class="mole-hole" style="top: 340px; left: 58%;"></div>
            <div class="mole-hole" style="top: 340px; left: 78%;"></div>
        </div>
    </div>

    <script>
        // 內嵌 game-utils.js 的函數
        function playSound(type = 'click') {
            const sounds = {
                click: 'data:audio/mp3;base64,//uQZAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAACcQAA',
                success: 'data:audio/mp3;base64,//uQZAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAACcQAA',
                error: 'data:audio/mp3;base64,//uQZAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAACcQAA'
            };
            const src = sounds[type] || sounds.click;
            const audio = new Audio(src);
            audio.volume = 0.3;
            audio.play().catch(() => {});
        }

        function celebrateConfetti(pieces = 80) {
            const containerId = 'confetti-container';
            let container = document.getElementById(containerId);
            if (!container) {
                container = document.createElement('div');
                container.id = containerId;
                container.className = 'confetti-container';
                document.body.appendChild(container);
            }
            for (let i = 0; i < pieces; i++) {
                const piece = document.createElement('div');
                piece.className = 'confetti-piece';
                const colors = ['#ffd06b', '#8b91ff', '#ff91a4', '#43e97b', '#4facfe'];
                piece.style.background = colors[Math.floor(Math.random() * colors.length)];
                piece.style.left = Math.random() * 100 + 'vw';
                const drift = (Math.random() * 40 - 20) + 'vw';
                piece.style.setProperty('--cx-end', drift);
                piece.style.animationDuration = (Math.random() * 1.5 + 1.5) + 's';
                container.appendChild(piece);
                setTimeout(() => piece.remove(), 3000);
            }
        }

        function getHighScore(key) {
            try {
                return Number(localStorage.getItem(key) || 0);
            } catch (_) {
                return 0;
            }
        }

        function setHighScore(key, value) {
            try {
                const current = getHighScore(key);
                if (value > current) localStorage.setItem(key, String(value));
            } catch (_) {}
        }
        
        let targetNumber = 12;
        let timeLeft = 30;
        let score = 0;
        let gameActive = false;
        let timer;
        let moleTimer;
        let currentMoles = [];
        const HS_KEY = 'hs-factor-mole';
        
        const difficulties = {
            easy: { time: 30, moleInterval: 1500, moleDuration: 2000 },
            medium: { time: 20, moleInterval: 1200, moleDuration: 1800 },
            hard: { time: 15, moleInterval: 1000, moleDuration: 1500 }
        };
        
        const holePositions = [
            // 第一行：5個洞
            { top: 40, left: 8 }, { top: 40, left: 28 }, { top: 40, left: 48 }, { top: 40, left: 68 }, { top: 40, left: 88 },
            // 第二行：4個洞
            { top: 140, left: 18 }, { top: 140, left: 38 }, { top: 140, left: 58 }, { top: 140, left: 78 },
            // 第三行：5個洞
            { top: 240, left: 8 }, { top: 240, left: 28 }, { top: 240, left: 48 }, { top: 240, left: 68 }, { top: 240, left: 88 },
            // 第四行：4個洞
            { top: 340, left: 18 }, { top: 340, left: 38 }, { top: 340, left: 58 }, { top: 340, left: 78 }
        ];

        function generateRandomNumber() {
            const numbers = [12, 18, 24, 30, 36, 42, 48, 56, 60, 72, 84, 96];
            return numbers[Math.floor(Math.random() * numbers.length)];
        }

        function getFactors(num) {
            const factors = [];
            for (let i = 1; i <= num; i++) {
                if (num % i === 0) {
                    factors.push(i);
                }
            }
            return factors;
        }

        function generateMoleNumber() {
            const factors = getFactors(targetNumber);
            const nonFactors = [];
            
            // 生成非因數
            for (let i = 1; i <= targetNumber + 10; i++) {
                if (targetNumber % i !== 0) {
                    nonFactors.push(i);
                }
            }
            
            // 70% 機率生成因數，30% 機率生成非因數
            if (Math.random() < 0.7) {
                return factors[Math.floor(Math.random() * factors.length)];
            } else {
                return nonFactors[Math.floor(Math.random() * nonFactors.length)];
            }
        }

        function createMole() {
            if (!gameActive) return;
            
            const number = generateMoleNumber();
            const isFactor = targetNumber % number === 0;
            
            // 隨機選擇一個洞
            const position = holePositions[Math.floor(Math.random() * holePositions.length)];
            
            const mole = document.createElement('div');
            mole.className = 'mole';
            mole.textContent = number;
            mole.style.top = position.top + 'px';
            mole.style.left = position.left + '%';
            mole.dataset.isFactor = isFactor;
            mole.dataset.number = number;
            
            mole.onclick = () => hitMole(mole, isFactor);
            
            document.getElementById('molesContainer').appendChild(mole);
            currentMoles.push(mole);
            
            // 地鼠停留時間後消失
            setTimeout(() => {
                if (mole.parentNode) {
                    mole.remove();
                    currentMoles = currentMoles.filter(m => m !== mole);
                }
            }, difficulties[document.getElementById('difficultySelect').value].moleDuration);
        }

        function hitMole(mole, isFactor) {
            if (!gameActive) return;
            
            mole.classList.add(isFactor ? 'hit' : 'miss');
            
            if (isFactor) {
                score += 10;
                showMessage(`正確！${mole.dataset.number} 是 ${targetNumber} 的因數！`, 'success');
                playSound('success');
            } else {
                score -= 5;
                showMessage(`錯誤！${mole.dataset.number} 不是 ${targetNumber} 的因數！`, 'error');
                playSound('error');
            }
            
            updateScore();
            
            setTimeout(() => {
                if (mole.parentNode) {
                    mole.remove();
                    currentMoles = currentMoles.filter(m => m !== mole);
                }
            }, 500);
        }

        function startTimer() {
            const difficulty = difficulties[document.getElementById('difficultySelect').value];
            timeLeft = difficulty.time;
            
            timer = setInterval(() => {
                timeLeft--;
                document.getElementById('timer').textContent = `時間: ${timeLeft}秒`;
                
                if (timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function startMoleSpawning() {
            const difficulty = difficulties[document.getElementById('difficultySelect').value];
            
            moleTimer = setInterval(() => {
                createMole();
            }, difficulty.moleInterval);
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `得分: ${score} <span class="high-score">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            targetNumber = generateRandomNumber();
            score = 0;
            
            // 清除所有現有地鼠
            currentMoles.forEach(mole => mole.remove());
            currentMoles = [];
            
            document.getElementById('targetNumber').textContent = targetNumber;
            updateScore();
            showMessage('遊戲開始！點擊因數地鼠！', 'success');
            
            clearInterval(timer);
            clearInterval(moleTimer);
            
            startTimer();
            startMoleSpawning();
        }

        function endGame() {
            gameActive = false;
            clearInterval(timer);
            clearInterval(moleTimer);
            
            // 清除所有地鼠
            currentMoles.forEach(mole => mole.remove());
            currentMoles = [];
            
            const high = getHighScore(HS_KEY);
            if (score > high) {
                showMessage(`新紀錄！得分: ${score}！`, 'game-over');
                celebrateConfetti(120);
            } else {
                showMessage(`遊戲結束！得分: ${score}`, 'error');
            }
        }

        // 初始化
        updateScore();
    </script>
</body>
</html>
