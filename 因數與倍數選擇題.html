<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數與倍數選擇題</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .page {
            max-width: 960px;
            margin: 0 auto;
            padding: 24px;
        }
        .card {
            background: #fff;
            border-radius: 18px;
            padding: 20px;
            box-shadow: 0 10px 24px rgba(0,0,0,0.12);
        }
        .toolbar {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        .option-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }
        .option {
            background: #ffffff;
            border: 2px solid #e9e9ff;
            border-radius: 14px;
            padding: 14px;
            text-align: center;
            font-weight: 800;
            cursor: pointer;
            transition: transform .12s ease, box-shadow .2s ease, border .2s ease;
        }
        .option:hover { transform: translateY(-2px); box-shadow: 0 6px 14px rgba(0,0,0,0.08); }
        .option.correct { border-color: #2ecc71; background:#e9f8ef; }
        .option.wrong { border-color: #e74c3c; background:#fdecea; }
        .status {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        .q-text {
            font-size: 1.3em;
            font-weight: 900;
            margin: 8px 0 4px;
        }
        .sub {
            color: #666;
            font-size: .95em;
        }
        .pill {
            display: inline-block;
            padding: 6px 10px;
            background: #ffffffcc;
            border: 2px dashed #e0e0ff;
            border-radius: 999px;
            font-weight: 800;
        }
        .footer-bar {
            display:flex; justify-content: space-between; align-items:center; margin-top:14px;
        }
        .img-wrap { text-align:center; margin-top: 10px; }
        .img-wrap img { width: 140px; height: 140px; object-fit: contain; }
    </style>
</head>
<body class="cute-container">
    <div class="page">
        <div style="display:flex; align-items:center; gap:10px; justify-content: space-between; margin-bottom: 12px;">
            <a class="back-btn" href="index.html">← 返回遊戲集合</a>
            <div class="high-score" id="highScoreBox">🏆 最高分：0</div>
        </div>

        <div class="card pop">
            <h1>🐿️ 因數與倍數選擇題</h1>

            <div class="toolbar">
                <div class="status">
                    <span class="badge">關卡：<span id="level">1</span></span>
                    <span class="badge">分數：<span id="score">0</span></span>
                    <span class="badge">時間：<span id="time">00:00</span></span>
                    <span class="badge">連擊：<span id="combo">0</span></span>
                    <span class="badge">❤️ <span id="lives">3</span></span>
                </div>
                <div class="status">
                    <select id="difficulty" class="btn btn-accent">
                        <option value="easy">簡單 (1-10)</option>
                        <option value="medium">中等 (1-50)</option>
                        <option value="hard">困難 (1-100)</option>
                    </select>
                    <select id="theme" class="btn btn-secondary">
                        <option value="forest">森林動物</option>
                        <option value="space">太空探險</option>
                        <option value="ocean">海洋世界</option>
                        <option value="cartoon">卡通人物</option>
                    </select>
                    <button id="startBtn" class="btn btn-primary">開始</button>
                </div>
            </div>

            <div id="questionBox">
                <div class="pill" id="qType">題型：—</div>
                <div class="q-text" id="qText">按「開始」產生題目</div>
                <div class="sub" id="qHint"></div>
                <div class="img-wrap"><img id="qImg" alt="cute" src="" style="display:none"></div>
                <div class="option-grid" id="options"></div>
            </div>

            <div class="footer-bar">
                <div id="msg"></div>
                <div>
                    <button id="skipBtn" class="btn">跳過</button>
                    <button id="explainBtn" class="btn">看解析</button>
                    <button id="saveBtn" class="btn">保存進度</button>
                </div>
            </div>
        </div>

        <div class="card" style="margin-top: 16px;">
            <h3>📝 錯題回顧</h3>
            <div id="review"></div>
        </div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, formatTime, getHighScore, setHighScore } from './game-utils.js';

        const els = {
          level: document.getElementById('level'),
          score: document.getElementById('score'),
          time: document.getElementById('time'),
          combo: document.getElementById('combo'),
          lives: document.getElementById('lives'),
          qType: document.getElementById('qType'),
          qText: document.getElementById('qText'),
          qHint: document.getElementById('qHint'),
          options: document.getElementById('options'),
          msg: document.getElementById('msg'),
          review: document.getElementById('review'),
          qImg: document.getElementById('qImg'),
          highScoreBox: document.getElementById('highScoreBox'),
          startBtn: document.getElementById('startBtn'),
          skipBtn: document.getElementById('skipBtn'),
          explainBtn: document.getElementById('explainBtn'),
          saveBtn: document.getElementById('saveBtn'),
          difficulty: document.getElementById('difficulty'),
          theme: document.getElementById('theme')
        };

        const HS_KEY = 'quiz_factors_multiples_highscore_v1';
        const SAVE_KEY = 'quiz_factors_multiples_progress_v1';
        els.highScoreBox.textContent = `🏆 最高分：${getHighScore(HS_KEY)}`;

        let state = {
          level: 1,
          score: 0,
          combo: 0,
          lives: 3,
          seconds: 0,
          running: false,
          wrongList: [],
          current: null
        };
        let timer = null;

        function rangeByDifficulty(diff) {
          if (diff === 'easy') return 10;
          if (diff === 'medium') return 50;
          return 100;
        }

        function randInt(n) { return 1 + Math.floor(Math.random() * n); }
        function pick(arr) { return arr[Math.floor(Math.random() * arr.length)]; }

        function getCuteImg(theme) {
          const map = {
            forest: [
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f43f-fe0f.png',
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f43c.png',
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f98a.png'
            ],
            space: [
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f680.png',
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f47d.png'
            ],
            ocean: [
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f433.png',
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f42c.png'
            ],
            cartoon: [
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f60a.png',
              'https://unpkg.com/emoji-datasource-apple/img/apple/64/1f61c.png'
            ]
          };
          const list = map[els.theme.value] || map.forest;
          return pick(list);
        }

        function isFactor(a, b) { return b % a === 0; }
        function isMultiple(a, b) { return a % b === 0; }

        function generateQuestion() {
          const max = rangeByDifficulty(els.difficulty.value);
          const type = Math.random() < 0.5 ? '因數' : '倍數';
          let stem = '';
          let options = [];
          let answer;
          if (type === '因數') {
            const b = randInt(max);
            stem = `下列哪一個是 ${b} 的因數？`;
            const factors = [];
            for (let i = 1; i <= b; i++) if (b % i === 0) factors.push(i);
            answer = pick(factors);
            const pool = new Set([answer]);
            while (pool.size < 4) {
              const n = randInt(max);
              if (b % n === 0 || pool.has(n)) continue;
              pool.add(n);
            }
            options = Array.from(pool);
          } else {
            const a = randInt(max);
            stem = `下列哪一個是 ${a} 的倍數？`;
            const k = randInt(9) + 1;
            answer = a * k;
            const pool = new Set([answer]);
            while (pool.size < 4) {
              const n = randInt(max * 3);
              if (n % a === 0 || pool.has(n)) continue;
              pool.add(n);
            }
            options = Array.from(pool);
          }
          options.sort(() => Math.random() - 0.5);
          return { type, stem, options, answer };
        }

        function renderQuestion(q) {
          els.qType.textContent = `題型：${q.type}`;
          els.qText.textContent = q.stem;
          els.qHint.textContent = q.type === '因數' ? '提示：因數可把數整除（無餘數）' : '提示：倍數是某數乘以整數的結果';
          const img = getCuteImg(els.theme.value);
          els.qImg.src = img; els.qImg.style.display = 'inline-block';
          els.options.innerHTML = '';
          q.options.forEach(n => {
            const btn = document.createElement('button');
            btn.className = 'option';
            btn.textContent = String(n);
            btn.addEventListener('click', () => onSelect(n));
            els.options.appendChild(btn);
          });
        }

        function onSelect(n) {
          if (!state.running || !state.current) return;
          const correct = n === state.current.answer;
          Array.from(els.options.children).forEach(ch => {
            const val = Number(ch.textContent);
            if (val === state.current.answer) ch.classList.add('correct');
            if (val === n && !correct) ch.classList.add('wrong');
            ch.disabled = true;
          });
          if (correct) {
            playSound('success');
            state.combo += 1;
            const gain = 10 + 2 * state.combo;
            state.score += gain;
            els.msg.textContent = `✅ 答對！+${gain} 分（連擊 ${state.combo}）`;
            celebrateConfetti(40);
          } else {
            playSound('error');
            state.combo = 0;
            state.lives -= 1;
            els.msg.textContent = `❌ 答錯！正確答案是 ${state.current.answer}`;
            state.wrongList.push({ ...state.current, chosen: n });
            if (state.lives <= 0) return endGame();
          }
          updateHUD();
          setTimeout(nextQuestion, 800);
        }

        function nextQuestion() {
          state.level += 1;
          state.current = generateQuestion();
          renderQuestion(state.current);
          updateHUD();
        }

        function updateHUD() {
          els.level.textContent = state.level;
          els.score.textContent = state.score;
          els.combo.textContent = state.combo;
          els.lives.textContent = state.lives;
          els.time.textContent = formatTime(state.seconds);
          els.highScoreBox.textContent = `🏆 最高分：${getHighScore(HS_KEY)}`;
        }

        function tick() {
          state.seconds += 1; els.time.textContent = formatTime(state.seconds);
        }

        function startGame() {
          state = { level: 1, score: 0, combo: 0, lives: 3, seconds: 0, running: true, wrongList: [], current: null };
          els.msg.textContent = '';
          clearInterval(timer); timer = setInterval(tick, 1000);
          state.current = generateQuestion();
          renderQuestion(state.current);
          updateHUD();
          playSound('click');
        }

        function endGame() {
          state.running = false;
          clearInterval(timer);
          setHighScore(HS_KEY, state.score);
          updateHUD();
          els.msg.innerHTML = `🎉 遊戲結束！本次得分 ${state.score} 分。`;
          renderReview();
        }

        function renderReview() {
          if (!state.wrongList.length) { els.review.textContent = '太棒了！沒有錯題～'; return; }
          els.review.innerHTML = '';
          state.wrongList.forEach((w, idx) => {
            const div = document.createElement('div');
            div.className = 'badge';
            const why = w.type === '因數' ? `${w.answer} 能整除題目中的數，其他不能` : `${w.answer} 是題目數的整數倍，其他不是`;
            div.textContent = `#${idx+1} ${w.stem} 你的答案：${w.chosen}。解析：${why}`;
            els.review.appendChild(div);
          });
        }

        function saveProgress() {
          const payload = { state, difficulty: els.difficulty.value, theme: els.theme.value };
          try { localStorage.setItem(SAVE_KEY, JSON.stringify(payload)); els.msg.textContent = '✅ 已保存進度'; } catch (_) {}
        }

        function loadProgress() {
          try {
            const raw = localStorage.getItem(SAVE_KEY);
            if (!raw) return;
            const { state: saved, difficulty, theme } = JSON.parse(raw);
            els.difficulty.value = difficulty || 'easy';
            els.theme.value = theme || 'forest';
            state = { ...saved, running: false };
            if (state.current) renderQuestion(state.current);
            updateHUD();
            els.msg.textContent = '📦 已載入上次進度，按「開始」繼續';
          } catch (_) {}
        }

        els.startBtn.addEventListener('click', startGame);
        els.skipBtn.addEventListener('click', () => { if (state.running) { state.combo = 0; els.msg.textContent = '⏭️ 已跳過此題'; nextQuestion(); }});
        els.explainBtn.addEventListener('click', () => { if (state.current) { els.msg.textContent = state.current.type === '因數' ? '因數是能把數整除的數' : '倍數是某數乘以整數得到的數'; }});
        els.saveBtn.addEventListener('click', saveProgress);

        window.addEventListener('beforeunload', saveProgress);
        loadProgress();
    </script>
</body>
</html>

