<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倍數接龍遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        select, input, button {
            font-size: 1.1em;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        button {
            background: #e67e22;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #d35400;
        }
        .current-number {
            font-size: 3em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }
        .sequence {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
            min-height: 60px;
        }
        .number-item {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 20px;
            font-size: 1.2em;
            animation: slideIn 0.3s ease;
        }
        @keyframes slideIn {
            0% { transform: translateX(-50px); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .timer {
            font-size: 1.5em;
            color: #f39c12;
            margin: 20px 0;
        }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🔗 倍數接龍遊戲</h1>
        
        <div class="controls">
            <label>選擇倍數：</label>
            <select id="multiplierSelect">
                <option value="2">2的倍數</option>
                <option value="3">3的倍數</option>
                <option value="4">4的倍數</option>
                <option value="5">5的倍數</option>
                <option value="6">6的倍數</option>
            </select>
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="timer" id="timer">時間: 60秒</div>
        <div class="score" id="score">連續正確: 0</div>
        <div class="current-number" id="currentNumber">2</div>
        <div class="message" id="message">輸入下一個倍數！</div>
        
        <div class="sequence" id="sequence"></div>
        
        <div class="controls">
            <input type="number" id="numberInput" placeholder="輸入數字" min="1">
            <button onclick="submitNumber()">提交</button>
        </div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore, formatTime } from './game-utils.js';
        let currentMultiplier = 2;
        let currentNumber = 2;
        let sequence = [];
        let score = 0;
        let timeLeft = 60;
        let timer;
        let gameActive = true;
        const HS_KEY = 'hs-multiple-chain-best';

        function startTimer() {
            timer = setInterval(() => {
                timeLeft--;
                document.getElementById('timer').textContent = `時間: ${timeLeft}秒`;
                
                if (timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function submitNumber() {
            if (!gameActive) return;
            
            const input = document.getElementById('numberInput');
            const number = parseInt(input.value);
            
            if (isNaN(number) || number < 1) {
                showMessage('請輸入有效的正整數！', 'error');
                return;
            }
            
            if (number % currentMultiplier === 0 && number > currentNumber) {
                sequence.push(currentNumber);
                currentNumber = number;
                score++;
                updateDisplay();
                input.value = '';
                showMessage('正確！繼續下一個！', 'success');
                playSound('success');
            } else if (number % currentMultiplier !== 0) {
                showMessage(`錯誤！${number} 不是 ${currentMultiplier} 的倍數！`, 'error');
                playSound('error');
            } else {
                showMessage(`錯誤！請輸入比 ${currentNumber} 更大的倍數！`, 'error');
                playSound('error');
            }
        }

        function updateDisplay() {
            document.getElementById('currentNumber').textContent = currentNumber;
            const best = getHighScore(HS_KEY);
            if (score > best) setHighScore(HS_KEY, score);
            const high = getHighScore(HS_KEY);
            document.getElementById('score').innerHTML = `連續正確: ${score} <span class="high-score">(最高：${high})</span>`;
            
            const sequenceDiv = document.getElementById('sequence');
            sequenceDiv.innerHTML = '';
            sequence.forEach(num => {
                const div = document.createElement('div');
                div.className = 'number-item';
                div.textContent = num;
                sequenceDiv.appendChild(div);
            });
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            currentMultiplier = parseInt(document.getElementById('multiplierSelect').value);
            currentNumber = currentMultiplier;
            sequence = [];
            score = 0;
            timeLeft = 60;
            
            updateDisplay();
            showMessage('輸入下一個倍數！', 'success');
            
            clearInterval(timer);
            startTimer();
        }

        function endGame() {
            gameActive = false;
            clearInterval(timer);
            showMessage(`遊戲結束！總共連續正確 ${score} 個倍數！`, 'error');
            celebrateConfetti(60);
        }

        // 鍵盤事件
        document.getElementById('numberInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                submitNumber();
            }
        });

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
