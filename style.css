/* Enhanced cute shared styles for all games */
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;700;900&family=Noto+Sans+TC:wght@400;700;900&family=Fredoka+One:wght@400&display=swap');

:root {
  --primary: #8b91ff;
  --primary-dark: #6366f1;
  --secondary: #ff91a4;
  --secondary-dark: #f472b6;
  --accent: #ffd06b;
  --accent-dark: #f59e0b;
  --success: #10b981;
  --success-light: #34d399;
  --error: #ef4444;
  --error-light: #f87171;
  --info: #3b82f6;
  --info-light: #60a5fa;
  --warning: #f59e0b;
  --bg-grad-1: #f6f3ff;
  --bg-grad-2: #ffe7f0;
  --bg-grad-3: #e0f2fe;
  --shadow-sm: 0 2px 8px rgba(0,0,0,0.08);
  --shadow-md: 0 4px 16px rgba(0,0,0,0.12);
  --shadow-lg: 0 8px 32px rgba(0,0,0,0.16);
  --shadow-xl: 0 12px 48px rgba(0,0,0,0.2);
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  font-family: 'Noto Sans TC', 'Nunito', 'Microsoft JhengHei', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
  color: #333;
  line-height: 1.6;
  scroll-behavior: smooth;
}

.cute-container {
  background: linear-gradient(135deg, var(--bg-grad-1) 0%, var(--bg-grad-2) 50%, var(--bg-grad-3) 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Animated background particles */
.cute-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(139, 145, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 145, 164, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 208, 107, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

/* Enhanced button styles */
.btn, button {
  border-radius: var(--border-radius-md);
  padding: 12px 24px;
  border: none;
  cursor: pointer;
  font-weight: 700;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
}

.btn:hover, button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.btn:active, button:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: #fff;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-dark) 100%);
  color: #fff;
}

.btn-accent {
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
  color: #593f00;
}

.btn-success {
  background: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
  color: #fff;
}

.btn-error {
  background: linear-gradient(135deg, var(--error) 0%, var(--error-light) 100%);
  color: #fff;
}

/* Button ripple effect */
.btn::before, button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.btn:active::before, button:active::before {
  width: 300px;
  height: 300px;
}

/* Enhanced UI components */
.chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #fff8e1 0%, #fef3c7 100%);
  color: #8a5a00;
  padding: 8px 16px;
  border-radius: 999px;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.chip:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.chip.primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
}

.chip.success {
  background: linear-gradient(135deg, var(--success) 0%, var(--success-light) 100%);
  color: white;
}

.high-score {
  font-size: 1.2em;
  font-weight: 900;
  color: var(--primary);
  margin-top: 8px;
  text-shadow: 0 2px 4px rgba(139, 145, 255, 0.3);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { text-shadow: 0 2px 4px rgba(139, 145, 255, 0.3); }
  to { text-shadow: 0 2px 8px rgba(139, 145, 255, 0.6), 0 0 16px rgba(139, 145, 255, 0.3); }
}

.message {
  padding: 16px 20px;
  border-radius: var(--border-radius-md);
  margin: 16px 0;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slideIn 0.3s ease;
}

.message.success {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  color: #065f46;
  border: 2px solid var(--success-light);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.message.error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #991b1b;
  border: 2px solid var(--error-light);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.message.info {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: #1e40af;
  border: 2px solid var(--info-light);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px dashed var(--primary);
  padding: 12px 16px;
  border-radius: var(--border-radius-lg);
  font-weight: 700;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-style: solid;
}

/* Enhanced animations */
.pop {
  animation: popInCute 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes popInCute {
  0% {
    transform: scale(0.3) rotate(-10deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.shake {
  animation: shakeCute 0.6s ease;
}

@keyframes shakeCute {
  0%, 100% { transform: translateX(0) rotate(0deg); }
  10% { transform: translateX(-8px) rotate(-1deg); }
  20% { transform: translateX(8px) rotate(1deg); }
  30% { transform: translateX(-6px) rotate(-1deg); }
  40% { transform: translateX(6px) rotate(1deg); }
  50% { transform: translateX(-4px) rotate(-0.5deg); }
  60% { transform: translateX(4px) rotate(0.5deg); }
  70% { transform: translateX(-2px) rotate(-0.5deg); }
  80% { transform: translateX(2px) rotate(0.5deg); }
  90% { transform: translateX(-1px) rotate(0deg); }
}

.bounce {
  animation: bounceCute 0.8s ease;
}

@keyframes bounceCute {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-20px); }
  70% { transform: translateY(-10px); }
  90% { transform: translateY(-4px); }
}

.pulse {
  animation: pulseCute 1.5s ease-in-out infinite;
}

@keyframes pulseCute {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

.fadeIn {
  animation: fadeInCute 0.5s ease;
}

@keyframes fadeInCute {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slideInLeft {
  animation: slideInLeftCute 0.5s ease;
}

@keyframes slideInLeftCute {
  from { opacity: 0; transform: translateX(-50px); }
  to { opacity: 1; transform: translateX(0); }
}

.slideInRight {
  animation: slideInRightCute 0.5s ease;
}

@keyframes slideInRightCute {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

.rotate {
  animation: rotateCute 2s linear infinite;
}

@keyframes rotateCute {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced confetti and particle effects */
.confetti-container {
  pointer-events: none;
  position: fixed;
  inset: 0;
  overflow: hidden;
  z-index: 9999;
}

.confetti-piece {
  position: absolute;
  width: 10px;
  height: 14px;
  opacity: 0.95;
  will-change: transform, opacity;
  animation: confetti-fall linear forwards;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes confetti-fall {
  0% {
    transform: translate3d(var(--cx, 0), -10%, 0) rotate(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate3d(var(--cx-mid, 0), 50vh, 0) rotate(360deg) scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: translate3d(var(--cx-end, 0), 110vh, 0) rotate(720deg) scale(0.8);
    opacity: 0;
  }
}

/* Game container enhancements */
.game-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-normal);
}

.game-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

/* Enhanced form elements */
input, select, textarea {
  border: 2px solid #e5e7eb;
  border-radius: var(--border-radius-md);
  padding: 12px 16px;
  font-size: 1rem;
  transition: all var(--transition-fast);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(139, 145, 255, 0.1);
  transform: translateY(-1px);
}

input:hover, select:hover, textarea:hover {
  border-color: var(--primary-dark);
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 4px;
  transition: width var(--transition-normal);
  position: relative;
}

.progress-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced navigation */
.back-btn {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%) !important;
  color: #fff !important;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: all var(--transition-fast);
}

.back-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
}

.back-btn::before {
  content: '←';
  font-size: 1.2em;
}

/* Enhanced headings */
h1, h2, h3 {
  font-family: 'Fredoka One', 'Nunito', 'Noto Sans TC', sans-serif;
  font-weight: 900;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h1 {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite alternate;
}

@keyframes gradientShift {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(10deg); }
}

/* Card hover effects */
.card, .game-card {
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card::before, .game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
  transition: left 0.5s ease;
  z-index: 1;
}

.card:hover::before, .game-card:hover::before {
  left: 100%;
}

/* Tooltip system */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .btn, button {
    padding: 14px 20px;
    font-size: 1.1rem;
    min-height: 48px;
  }

  .game-container {
    margin: 10px;
    padding: 20px;
  }

  h1 {
    font-size: 2rem;
  }

  .confetti-piece {
    width: 8px;
    height: 10px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-grad-1: #1e1b4b;
    --bg-grad-2: #312e81;
    --bg-grad-3: #1e40af;
  }

  .game-container {
    background: rgba(30, 27, 75, 0.95);
    color: #f8fafc;
  }

  input, select, textarea {
    background: rgba(30, 27, 75, 0.8);
    color: #f8fafc;
    border-color: #4c1d95;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .cute-container::before {
    animation: none;
  }
}

/* Focus indicators for keyboard navigation */
.btn:focus-visible, button:focus-visible {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

input:focus-visible, select:focus-visible, textarea:focus-visible {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}


