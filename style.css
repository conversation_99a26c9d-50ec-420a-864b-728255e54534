/* Cute shared styles for all games */
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;700;900&family=Noto+Sans+TC:wght@400;700;900&display=swap');

:root {
  --primary: #8b91ff;
  --secondary: #ff91a4;
  --accent: #ffd06b;
  --success: #2ecc71;
  --error: #e74c3c;
  --info: #3498db;
  --bg-grad-1: #f6f3ff;
  --bg-grad-2: #ffe7f0;
}

* { box-sizing: border-box; }

html, body {
  font-family: 'Noto Sans TC', 'Nunito', 'Microsoft JhengHei', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
  color: #333;
}

.cute-container {
  background: linear-gradient(135deg, var(--bg-grad-1) 0%, var(--bg-grad-2) 100%);
  min-height: 100vh;
}

.btn, button {
  border-radius: 12px;
  padding: 10px 18px;
  border: none;
  cursor: pointer;
  font-weight: 700;
  transition: transform 0.12s ease, box-shadow 0.2s ease, background 0.2s ease;
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.btn:hover, button:hover { transform: translateY(-1px); }
.btn:active, button:active { transform: translateY(0); }

.btn-primary { background: var(--primary); color: #fff; }
.btn-secondary { background: var(--secondary); color: #fff; }
.btn-accent { background: var(--accent); color: #593f00; }

.chip {
  display: inline-block;
  background: #fff8e1;
  color: #8a5a00;
  padding: 6px 12px;
  border-radius: 999px;
  font-weight: 700;
}

.high-score {
  font-size: 1.1em;
  color: #a56bee;
  margin-top: 6px;
}

.message.success { background: #e9f8ef; color: #11633a; border: 2px solid #b9eccd; }
.message.error { background: #fdecea; color: #8a1c14; border: 2px solid #f5c6cb; }

.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #ffffffcc;
  border: 2px dashed #e0e0ff;
  padding: 8px 12px;
  border-radius: 14px;
}

/* Cute animations */
.pop {
  animation: popInCute 300ms ease;
}
@keyframes popInCute {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.shake {
  animation: shakeCute 0.4s ease;
}
@keyframes shakeCute {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-6px); }
  75% { transform: translateX(6px); }
}

/* Confetti */
.confetti-container {
  pointer-events: none;
  position: fixed;
  inset: 0;
  overflow: hidden;
  z-index: 9999;
}
.confetti-piece {
  position: absolute;
  width: 10px;
  height: 14px;
  opacity: 0.95;
  will-change: transform, opacity;
  animation: confetti-fall linear forwards;
  border-radius: 3px;
}
@keyframes confetti-fall {
  0% { transform: translate3d(var(--cx, 0), -10%, 0) rotate(0deg); opacity: 1; }
  100% { transform: translate3d(var(--cx-end, 0), 110vh, 0) rotate(720deg); opacity: 0.9; }
}

.back-btn {
  background: #bdc3c7 !important;
  color: #fff !important;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 10px;
  box-shadow: 0 3px 8px rgba(0,0,0,0.12);
}

/* Cute headings */
h1, h2, h3 { font-family: 'Nunito', 'Noto Sans TC', sans-serif; font-weight: 900; letter-spacing: 0.5px; }


