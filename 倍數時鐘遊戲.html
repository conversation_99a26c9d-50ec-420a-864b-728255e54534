<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倍數時鐘遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        select, button {
            font-size: 1.1em;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        button {
            background: #9b59b6;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #8e44ad;
        }
        .clock-container {
            position: relative;
            width: 300px;
            height: 300px;
            margin: 30px auto;
            border: 8px solid #333;
            border-radius: 50%;
            background: #f8f9fa;
        }
        .clock-face {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 280px;
            height: 280px;
        }
        .hour-markers {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .hour-marker {
            position: absolute;
            width: 4px;
            height: 20px;
            background: #333;
            transform-origin: 2px 140px;
        }
        .hour-number {
            position: absolute;
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            transform: translate(-50%, -50%);
        }
        .clock-hands {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .hour-hand {
            position: absolute;
            width: 6px;
            height: 60px;
            background: #333;
            transform-origin: 3px 0;
            border-radius: 3px;
        }
        .minute-hand {
            position: absolute;
            width: 4px;
            height: 80px;
            background: #e74c3c;
            transform-origin: 2px 0;
            border-radius: 2px;
        }
        .center-dot {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #333;
            border-radius: 50%;
            top: -6px;
            left: -6px;
        }
        .time-display {
            font-size: 2em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }
        .question {
            font-size: 1.3em;
            margin: 20px 0;
            color: #333;
        }
        .answer-input {
            font-size: 1.2em;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 10px;
            width: 100px;
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🕐 倍數時鐘遊戲</h1>
        
        <div class="controls">
            <label>選擇倍數：</label>
            <select id="multiplierSelect">
                <option value="2">2的倍數</option>
                <option value="3">3的倍數</option>
                <option value="4">4的倍數</option>
                <option value="5">5的倍數</option>
                <option value="6">6的倍數</option>
            </select>
            <button onclick="newGame()">新遊戲</button>
            <button onclick="setRandomTime()">隨機時間</button>
        </div>
        
        <div class="clock-container">
            <div class="clock-face">
                <div class="hour-markers" id="hourMarkers"></div>
                <div class="clock-hands">
                    <div class="hour-hand" id="hourHand"></div>
                    <div class="minute-hand" id="minuteHand"></div>
                    <div class="center-dot"></div>
                </div>
            </div>
        </div>
        
        <div class="time-display" id="timeDisplay">12:00</div>
        <div class="question" id="question">這個時間是2的倍數嗎？</div>
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">調整時鐘指針或點擊隨機時間！</div>
        
        <div class="controls">
            <input type="number" class="answer-input" id="hourInput" placeholder="時" min="1" max="12">
            <input type="number" class="answer-input" id="minuteInput" placeholder="分" min="0" max="59">
            <button onclick="setTime()">設定時間</button>
            <button onclick="checkAnswer()">檢查答案</button>
        </div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let currentMultiplier = 2;
        let currentHour = 12;
        let currentMinute = 0;
        let score = 0;
        let gameActive = true;
        const HS_KEY = 'hs-multiple-clock';

        function createHourMarkers() {
            const markersContainer = document.getElementById('hourMarkers');
            markersContainer.innerHTML = '';
            
            for (let i = 1; i <= 12; i++) {
                // 創建時標
                const marker = document.createElement('div');
                marker.className = 'hour-marker';
                marker.style.transform = `rotate(${i * 30}deg)`;
                markersContainer.appendChild(marker);
                
                // 創建數字
                const number = document.createElement('div');
                number.className = 'hour-number';
                number.textContent = i;
                const angle = (i * 30 - 90) * Math.PI / 180;
                const radius = 120;
                number.style.left = `${140 + radius * Math.cos(angle)}px`;
                number.style.top = `${140 + radius * Math.sin(angle)}px`;
                markersContainer.appendChild(number);
            }
        }

        function updateClock() {
            const hourHand = document.getElementById('hourHand');
            const minuteHand = document.getElementById('minuteHand');
            const timeDisplay = document.getElementById('timeDisplay');
            
            // 計算角度
            const hourAngle = (currentHour % 12) * 30 + currentMinute * 0.5;
            const minuteAngle = currentMinute * 6;
            
            // 設定指針角度
            hourHand.style.transform = `rotate(${hourAngle}deg)`;
            minuteHand.style.transform = `rotate(${minuteAngle}deg)`;
            
            // 更新時間顯示
            const displayHour = currentHour.toString().padStart(2, '0');
            const displayMinute = currentMinute.toString().padStart(2, '0');
            timeDisplay.textContent = `${displayHour}:${displayMinute}`;
            
            // 更新問題
            document.getElementById('question').textContent = `這個時間是${currentMultiplier}的倍數嗎？`;
        }

        function setTime() {
            const hour = parseInt(document.getElementById('hourInput').value);
            const minute = parseInt(document.getElementById('minuteInput').value);
            
            if (isNaN(hour) || isNaN(minute) || hour < 1 || hour > 12 || minute < 0 || minute > 59) {
                showMessage('請輸入有效的時間！', 'error');
                return;
            }
            
            currentHour = hour;
            currentMinute = minute;
            updateClock();
            showMessage('時間已設定！', 'success');
        }

        function setRandomTime() {
            currentHour = Math.floor(Math.random() * 12) + 1;
            currentMinute = Math.floor(Math.random() * 60);
            updateClock();
            showMessage('隨機時間已設定！', 'success');
            playSound('click');
        }

        function checkAnswer() {
            if (!gameActive) return;
            
            const isMultiple = (currentHour % currentMultiplier === 0) || (currentMinute % currentMultiplier === 0);
            const userAnswer = document.getElementById('answerInput') ? document.getElementById('answerInput').value : '';
            
            // 簡單的答案檢查（可以擴展為更複雜的邏輯）
            if (isMultiple) {
                score += 10;
                showMessage('正確！這個時間包含倍數！', 'success');
                playSound('success');
                celebrateConfetti(50);
            } else {
                showMessage('錯誤！這個時間不包含倍數！', 'error');
                playSound('error');
            }
            
            updateScore();
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `得分: ${score} <span class=\"high-score\">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            currentMultiplier = parseInt(document.getElementById('multiplierSelect').value);
            currentHour = 12;
            currentMinute = 0;
            score = 0;
            
            updateClock();
            updateScore();
            showMessage('調整時鐘指針或點擊隨機時間！', 'success');
        }

        // 初始化遊戲
        createHourMarkers();
        newGame();
    </script>
</body>
</html>
