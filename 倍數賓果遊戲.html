<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倍數賓果遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        select, button {
            font-size: 1.1em;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #c0392b;
        }
        .bingo-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 5px;
            margin: 30px auto;
            max-width: 300px;
        }
        .bingo-cell {
            width: 50px;
            height: 50px;
            border: 2px solid #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s;
            background: #ecf0f1;
        }
        .bingo-cell:hover {
            transform: scale(1.05);
        }
        .bingo-cell.marked {
            background: #2ecc71;
            color: white;
            animation: bounce 0.5s ease;
        }
        .bingo-cell.wrong {
            background: #e74c3c;
            color: white;
            animation: shake 0.5s ease;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .called-number {
            font-size: 2em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .bingo {
            background: #fff3cd;
            color: #856404;
            border: 3px solid #ffc107;
            animation: celebrate 1s ease infinite;
        }
        @keyframes celebrate {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🎯 倍數賓果遊戲</h1>
        
        <div class="controls">
            <label>選擇倍數：</label>
            <select id="multiplierSelect">
                <option value="2">2的倍數</option>
                <option value="3">3的倍數</option>
                <option value="4">4的倍數</option>
                <option value="5">5的倍數</option>
                <option value="6">6的倍數</option>
            </select>
            <button onclick="newGame()">新遊戲</button>
            <button onclick="callNumber()">叫號</button>
        </div>
        
        <div class="called-number" id="calledNumber">點擊「叫號」開始！</div>
        <div class="score" id="score">標記正確: 0</div>
        <div class="message" id="message">標記正確的倍數！</div>
        
        <div class="bingo-grid" id="bingoGrid"></div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let currentMultiplier = 2;
        let bingoNumbers = [];
        let calledNumbers = [];
        let score = 0;
        let gameActive = true;
        const HS_KEY = 'hs-multiple-bingo';

        function generateBingoNumbers() {
            const numbers = [];
            for (let i = 1; i <= 25; i++) {
                numbers.push(i);
            }
            // 洗牌
            for (let i = numbers.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
            }
            return numbers.slice(0, 25);
        }

        function createBingoGrid() {
            const grid = document.getElementById('bingoGrid');
            grid.innerHTML = '';
            
            bingoNumbers.forEach((number, index) => {
                const cell = document.createElement('div');
                cell.className = 'bingo-cell';
                cell.textContent = number;
                cell.onclick = () => markCell(index, cell, number);
                grid.appendChild(cell);
            });
        }

        function markCell(index, cellElement, number) {
            if (!gameActive) return;
            
            if (calledNumbers.length === 0) {
                showMessage('請先叫號！', 'error');
                return;
            }
            
            const lastCalled = calledNumbers[calledNumbers.length - 1];
            
            if (number === lastCalled && number % currentMultiplier === 0) {
                cellElement.classList.add('marked');
                score += 10;
                showMessage(`正確！${number} 是 ${currentMultiplier} 的倍數！`, 'success');
                playSound('success');
                updateScore();
            } else if (number === lastCalled && number % currentMultiplier !== 0) {
                cellElement.classList.add('wrong');
                showMessage(`錯誤！${number} 不是 ${currentMultiplier} 的倍數！`, 'error');
                playSound('error');
                setTimeout(() => {
                    cellElement.classList.remove('wrong');
                }, 1000);
            } else {
                showMessage(`請標記剛叫到的數字 ${lastCalled}！`, 'error');
            }
        }

        function callNumber() {
            if (!gameActive) return;
            
            const availableNumbers = bingoNumbers.filter(num => !calledNumbers.includes(num));
            if (availableNumbers.length === 0) {
                showMessage('所有數字都叫完了！', 'error');
                return;
            }
            
            const randomIndex = Math.floor(Math.random() * availableNumbers.length);
            const calledNumber = availableNumbers[randomIndex];
            calledNumbers.push(calledNumber);
            
            document.getElementById('calledNumber').textContent = `叫到: ${calledNumber}`;
            showMessage(`請標記 ${calledNumber}！`, 'success');
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `標記正確: ${score} <span class=\"high-score\">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            currentMultiplier = parseInt(document.getElementById('multiplierSelect').value);
            bingoNumbers = generateBingoNumbers();
            calledNumbers = [];
            score = 0;
            
            createBingoGrid();
            updateScore();
            document.getElementById('calledNumber').textContent = '點擊「叫號」開始！';
            showMessage('標記正確的倍數！', 'success');
        }

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
