<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數大富翁遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #e67e22;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #d35400;
        }
        .board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 5px;
            margin: 30px auto;
            max-width: 600px;
        }
        .cell {
            width: 60px;
            height: 60px;
            border: 2px solid #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s;
            background: #ecf0f1;
            position: relative;
        }
        .cell:hover {
            transform: scale(1.05);
        }
        .cell.visited {
            background: #2ecc71;
            color: white;
        }
        .cell.current {
            background: #e74c3c;
            color: white;
            animation: pulse 1s ease infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .player {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #f39c12;
            border-radius: 50%;
            top: 5px;
            right: 5px;
        }
        .question-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .question-content {
            background: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .question-text {
            font-size: 1.3em;
            margin: 20px 0;
            color: #333;
        }
        .answer-input {
            font-size: 1.2em;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 10px;
            width: 100px;
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🎲 因數大富翁遊戲</h1>
        
        <div class="controls">
            <button onclick="rollDice()">擲骰子</button>
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">擲骰子開始遊戲！</div>
        
        <div class="board" id="board"></div>
    </div>

    <div class="question-modal" id="questionModal">
        <div class="question-content">
            <h3>因數問題</h3>
            <div class="question-text" id="questionText">12的因數有哪些？</div>
            <input type="text" class="answer-input" id="answerInput" placeholder="輸入答案">
            <br>
            <button onclick="submitAnswer()">提交答案</button>
        </div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let playerPosition = 0;
        let score = 0;
        let gameActive = true;
        let currentQuestion = null;
        let correctAnswer = null;
        const HS_KEY = 'hs-factor-monopoly';

        const questions = [
            { question: "12的因數有哪些？", answer: "1,2,3,4,6,12", alternatives: ["1,2,3,4,6,12", "2,3,4,6", "1,2,3,4,6"] },
            { question: "18的因數有哪些？", answer: "1,2,3,6,9,18", alternatives: ["1,2,3,6,9,18", "2,3,6,9", "1,2,3,6,9"] },
            { question: "24的因數有哪些？", answer: "1,2,3,4,6,8,12,24", alternatives: ["1,2,3,4,6,8,12,24", "2,3,4,6,8,12", "1,2,3,4,6,8,12"] },
            { question: "30的因數有哪些？", answer: "1,2,3,5,6,10,15,30", alternatives: ["1,2,3,5,6,10,15,30", "2,3,5,6,10,15", "1,2,3,5,6,10,15"] },
            { question: "36的因數有哪些？", answer: "1,2,3,4,6,9,12,18,36", alternatives: ["1,2,3,4,6,9,12,18,36", "2,3,4,6,9,12,18", "1,2,3,4,6,9,12,18"] }
        ];

        function createBoard() {
            const board = document.getElementById('board');
            board.innerHTML = '';
            
            for (let i = 0; i < 32; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.textContent = i + 1;
                cell.id = `cell-${i}`;
                board.appendChild(cell);
            }
            
            updatePlayerPosition();
        }

        function updatePlayerPosition() {
            // 清除所有當前位置標記
            document.querySelectorAll('.cell').forEach(cell => {
                cell.classList.remove('current');
                const player = cell.querySelector('.player');
                if (player) player.remove();
            });
            
            // 標記當前位置
            const currentCell = document.getElementById(`cell-${playerPosition}`);
            if (currentCell) {
                currentCell.classList.add('current');
                const player = document.createElement('div');
                player.className = 'player';
                currentCell.appendChild(player);
            }
        }

        function rollDice() {
            if (!gameActive) return;
            
            const dice = Math.floor(Math.random() * 6) + 1;
            playerPosition = (playerPosition + dice) % 32;
            
            showMessage(`擲出 ${dice} 點！移動到位置 ${playerPosition + 1}`, 'success');
            playSound('click');
            updatePlayerPosition();
            
            // 檢查是否遇到問題
            if (playerPosition % 5 === 0) {
                showQuestion();
            }
        }

        function showQuestion() {
            const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
            currentQuestion = randomQuestion;
            correctAnswer = randomQuestion.answer;
            
            document.getElementById('questionText').textContent = randomQuestion.question;
            document.getElementById('answerInput').value = '';
            document.getElementById('questionModal').style.display = 'flex';
        }

        function submitAnswer() {
            const userAnswer = document.getElementById('answerInput').value.trim();
            
            if (userAnswer === correctAnswer) {
                score += 20;
                showMessage('回答正確！獲得20分！', 'success');
                playSound('success');
                updateScore();
            } else {
                showMessage(`回答錯誤！正確答案是：${correctAnswer}`, 'error');
                playSound('error');
            }
            
            document.getElementById('questionModal').style.display = 'none';
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `得分: ${score} <span class=\"high-score\">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            playerPosition = 0;
            score = 0;
            
            createBoard();
            updateScore();
            showMessage('擲骰子開始遊戲！', 'success');
        }

        // 鍵盤事件
        document.getElementById('answerInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                submitAnswer();
            }
        });

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
