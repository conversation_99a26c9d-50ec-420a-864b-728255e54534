<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數配對記憶卡遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #9b59b6;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #8e44ad;
        }
        .memory-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 30px auto;
            max-width: 400px;
        }
        .card {
            width: 80px;
            height: 80px;
            background: #3498db;
            color: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s;
            transform: rotateY(180deg);
        }
        .card.flipped {
            transform: rotateY(0deg);
            background: #2ecc71;
        }
        .card.matched {
            background: #27ae60;
            cursor: default;
        }
        .card.wrong {
            background: #e74c3c;
            animation: shake 0.5s ease;
        }
        @keyframes shake {
            0%, 100% { transform: rotateY(0deg) translateX(0); }
            25% { transform: rotateY(0deg) translateX(-5px); }
            75% { transform: rotateY(0deg) translateX(5px); }
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🧠 因數配對記憶卡遊戲</h1>
        
        <div class="controls">
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="score" id="score">配對成功: 0/8</div>
        <div class="message" id="message">翻開卡片找到因數配對！</div>
        
        <div class="memory-grid" id="memoryGrid"></div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let cards = [];
        let flippedCards = [];
        let matchedPairs = 0;
        let gameActive = true;
        const HS_KEY = 'hs-factor-memory-best';

        const factorPairs = [
            [12, 3], [12, 4], [12, 6],
            [18, 2], [18, 3], [18, 6], [18, 9],
            [24, 2], [24, 3], [24, 4], [24, 6], [24, 8], [24, 12],
            [30, 2], [30, 3], [30, 5], [30, 6], [30, 10], [30, 15]
        ];

        function createCards() {
            const selectedPairs = factorPairs.slice(0, 8);
            cards = [];
            
            selectedPairs.forEach(pair => {
                cards.push({ value: pair[0], type: 'number', matched: false });
                cards.push({ value: pair[1], type: 'factor', matched: false });
            });
            
            // 洗牌
            for (let i = cards.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [cards[i], cards[j]] = [cards[j], cards[i]];
            }
        }

        function createGrid() {
            const grid = document.getElementById('memoryGrid');
            grid.innerHTML = '';
            
            cards.forEach((card, index) => {
                const cardElement = document.createElement('div');
                cardElement.className = 'card';
                cardElement.textContent = '?';
                cardElement.onclick = () => flipCard(index);
                grid.appendChild(cardElement);
            });
        }

        function flipCard(index) {
            if (!gameActive || flippedCards.length >= 2 || cards[index].matched) return;
            
            const cardElement = document.querySelectorAll('.card')[index];
            cardElement.classList.add('flipped');
            cardElement.textContent = cards[index].value;
            
            flippedCards.push({ index, card: cards[index] });
            
            if (flippedCards.length === 2) {
                setTimeout(checkMatch, 1000);
            }
        }

        function checkMatch() {
            const [card1, card2] = flippedCards;
            const isMatch = checkFactorMatch(card1.card, card2.card);
            
            if (isMatch) {
                document.querySelectorAll('.card')[card1.index].classList.add('matched');
                document.querySelectorAll('.card')[card2.index].classList.add('matched');
                cards[card1.index].matched = true;
                cards[card2.index].matched = true;
                matchedPairs++;
                updateScore();
                showMessage('配對成功！', 'success');
                playSound('success');
                
                if (matchedPairs === 8) {
                    showMessage('恭喜！所有配對都完成了！', 'success');
                    celebrateConfetti(100);
                    gameActive = false;
                }
            } else {
                document.querySelectorAll('.card')[card1.index].classList.add('wrong');
                document.querySelectorAll('.card')[card2.index].classList.add('wrong');
                showMessage('配對失敗！', 'error');
                playSound('error');
                
                setTimeout(() => {
                    document.querySelectorAll('.card')[card1.index].classList.remove('flipped', 'wrong');
                    document.querySelectorAll('.card')[card2.index].classList.remove('flipped', 'wrong');
                    document.querySelectorAll('.card')[card1.index].textContent = '?';
                    document.querySelectorAll('.card')[card2.index].textContent = '?';
                }, 1000);
            }
            
            flippedCards = [];
        }

        function checkFactorMatch(card1, card2) {
            if (card1.type === 'number' && card2.type === 'factor') {
                return card1.value % card2.value === 0;
            } else if (card1.type === 'factor' && card2.type === 'number') {
                return card2.value % card1.value === 0;
            }
            return false;
        }

        function updateScore() {
            const best = getHighScore(HS_KEY);
            if (matchedPairs > best) setHighScore(HS_KEY, matchedPairs);
            const high = getHighScore(HS_KEY);
            document.getElementById('score').innerHTML = `配對成功: ${matchedPairs}/8 <span class="high-score">(最佳：${high}/8)</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            matchedPairs = 0;
            flippedCards = [];
            createCards();
            createGrid();
            updateScore();
            showMessage('翻開卡片找到因數配對！', 'success');
        }

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
