<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倍數音樂椅遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        select, button {
            font-size: 1.1em;
            padding: 10px 15px;
            margin: 10px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        button {
            background: #e74c3c;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #c0392b;
        }
        .music-controls {
            margin: 20px 0;
        }
        .music-button {
            background: #9b59b6;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 15px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        .music-button:hover {
            background: #8e44ad;
            transform: scale(1.05);
        }
        .music-button.playing {
            background: #e74c3c;
            animation: pulse 1s ease infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .chairs-container {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin: 30px auto;
            max-width: 500px;
        }
        .chair {
            width: 80px;
            height: 80px;
            border: 3px solid #333;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s;
            background: #ecf0f1;
            color: #333;
            position: relative;
        }
        .chair:hover {
            transform: scale(1.1);
        }
        .chair.occupied {
            background: #2ecc71;
            color: white;
            animation: bounce 0.5s ease;
        }
        .chair.wrong {
            background: #e74c3c;
            color: white;
            animation: shake 0.5s ease;
        }
        .chair.correct {
            background: #27ae60;
            color: white;
            animation: correct 0.5s ease;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        @keyframes correct {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        .player {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #f39c12;
            border-radius: 50%;
            top: -15px;
            right: -15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            color: white;
            font-weight: bold;
        }
        .current-number {
            font-size: 3em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
            animation: pulse 1s ease infinite;
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🎵 倍數音樂椅遊戲</h1>
        
        <div class="controls">
            <label>選擇倍數：</label>
            <select id="multiplierSelect">
                <option value="2">2的倍數</option>
                <option value="3">3的倍數</option>
                <option value="4">4的倍數</option>
                <option value="5">5的倍數</option>
                <option value="6">6的倍數</option>
            </select>
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="music-controls">
            <button class="music-button" id="musicButton" onclick="toggleMusic()">🎵 開始音樂</button>
        </div>
        
        <div class="current-number" id="currentNumber">12</div>
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">音樂停止時，點擊正確的倍數椅子！</div>
        
        <div class="chairs-container" id="chairsContainer"></div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let currentMultiplier = 2;
        let currentNumber = 12;
        let chairs = [];
        let score = 0;
        let gameActive = true;
        let musicPlaying = false;
        let musicTimer = null;
        let gameTimer = null;
        const HS_KEY = 'hs-music-chairs';

        function createChairs() {
            const container = document.getElementById('chairsContainer');
            container.innerHTML = '';
            chairs = [];
            
            for (let i = 1; i <= 20; i++) {
                const chair = document.createElement('div');
                chair.className = 'chair';
                chair.textContent = i;
                chair.onclick = () => selectChair(i, chair);
                container.appendChild(chair);
                chairs.push({ number: i, element: chair, occupied: false });
            }
        }

        function selectChair(number, chairElement) {
            if (!gameActive || musicPlaying) return;
            
            if (number % currentMultiplier === 0) {
                chairElement.classList.add('correct');
                score += 10;
                showMessage(`正確！${number} 是 ${currentMultiplier} 的倍數！`, 'success');
                playSound('success');
                updateScore();
                
                // 添加玩家標記
                if (!chairElement.querySelector('.player')) {
                    const player = document.createElement('div');
                    player.className = 'player';
                    player.textContent = 'P';
                    chairElement.appendChild(player);
                }
            } else {
                chairElement.classList.add('wrong');
                showMessage(`錯誤！${number} 不是 ${currentMultiplier} 的倍數！`, 'error');
                playSound('error');
                setTimeout(() => {
                    chairElement.classList.remove('wrong');
                }, 1000);
            }
        }

        function toggleMusic() {
            const musicButton = document.getElementById('musicButton');
            
            if (!musicPlaying) {
                startMusic();
            } else {
                stopMusic();
            }
        }

        function startMusic() {
            musicPlaying = true;
            const musicButton = document.getElementById('musicButton');
            musicButton.textContent = '🎵 音樂播放中...';
            musicButton.classList.add('playing');
            playSound('click');
            
            // 隨機播放時間 3-8秒
            const playTime = Math.random() * 5000 + 3000;
            musicTimer = setTimeout(() => {
                stopMusic();
            }, playTime);
        }

        function stopMusic() {
            musicPlaying = false;
            const musicButton = document.getElementById('musicButton');
            musicButton.textContent = '🎵 開始音樂';
            musicButton.classList.remove('playing');
            
            if (musicTimer) {
                clearTimeout(musicTimer);
                musicTimer = null;
            }
            
            // 生成新的數字
            generateNewNumber();
        }

        function generateNewNumber() {
            currentNumber = Math.floor(Math.random() * 50) + 1;
            document.getElementById('currentNumber').textContent = currentNumber;
            showMessage(`音樂停止！點擊 ${currentMultiplier} 的倍數椅子！`, 'success');
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `得分: ${score} <span class=\"high-score\">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            currentMultiplier = parseInt(document.getElementById('multiplierSelect').value);
            score = 0;
            
            createChairs();
            generateNewNumber();
            updateScore();
            showMessage('音樂停止時，點擊正確的倍數椅子！', 'success');
        }

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
