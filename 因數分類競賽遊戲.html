<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數分類競賽遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background: #e67e22;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #d35400;
        }
        .target-number {
            font-size: 2.5em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }
        .numbers-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin: 30px auto;
            max-width: 600px;
        }
        .number-card {
            width: 80px;
            height: 80px;
            border: 3px solid #333;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s;
            background: #ecf0f1;
            color: #333;
        }
        .number-card:hover {
            transform: scale(1.1);
        }
        .number-card.selected {
            background: #3498db;
            color: white;
            animation: bounce 0.5s ease;
        }
        .number-card.correct {
            background: #2ecc71;
            color: white;
            animation: correct 0.5s ease;
        }
        .number-card.wrong {
            background: #e74c3c;
            color: white;
            animation: shake 0.5s ease;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        @keyframes correct {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .categories {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
        }
        .category {
            background: #f8f9fa;
            border: 3px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            min-height: 100px;
            min-width: 150px;
            text-align: center;
        }
        .category h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .category.correct {
            border-color: #2ecc71;
            background: #d4edda;
        }
        .category.wrong {
            border-color: #e74c3c;
            background: #f8d7da;
        }
        .category-numbers {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 5px;
        }
        .category-number {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 10px;
            font-size: 0.9em;
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🏆 因數分類競賽遊戲</h1>
        
        <div class="controls">
            <button onclick="newGame()">新遊戲</button>
            <button onclick="checkAnswer()">檢查答案</button>
        </div>
        
        <div class="target-number" id="targetNumber">12</div>
        <div class="score" id="score">得分: 0</div>
        <div class="message" id="message">將數字拖拽到正確的分類中！</div>
        
        <div class="numbers-grid" id="numbersGrid"></div>
        
        <div class="categories">
            <div class="category" id="factorsCategory">
                <h3>因數</h3>
                <div class="category-numbers" id="factorsNumbers"></div>
            </div>
            <div class="category" id="multiplesCategory">
                <h3>倍數</h3>
                <div class="category-numbers" id="multiplesNumbers"></div>
            </div>
            <div class="category" id="neitherCategory">
                <h3>都不是</h3>
                <div class="category-numbers" id="neitherNumbers"></div>
            </div>
        </div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let targetNumber = 12;
        let numbers = [];
        let selectedNumbers = [];
        let score = 0;
        let gameActive = true;
        const HS_KEY = 'hs-factor-classify';

        function generateNumbers() {
            const allNumbers = [];
            // 生成因數
            for (let i = 1; i <= targetNumber; i++) {
                if (targetNumber % i === 0) {
                    allNumbers.push({ value: i, type: 'factor' });
                }
            }
            // 生成倍數
            for (let i = 1; i <= 5; i++) {
                allNumbers.push({ value: targetNumber * i, type: 'multiple' });
            }
            // 生成其他數字
            const otherNumbers = [7, 11, 13, 17, 19, 23, 25, 26, 28, 29];
            otherNumbers.forEach(num => {
                if (num !== targetNumber && !allNumbers.some(n => n.value === num)) {
                    allNumbers.push({ value: num, type: 'neither' });
                }
            });
            
            // 洗牌並選擇12個
            for (let i = allNumbers.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [allNumbers[i], allNumbers[j]] = [allNumbers[j], allNumbers[i]];
            }
            
            return allNumbers.slice(0, 12);
        }

        function createNumbersGrid() {
            const grid = document.getElementById('numbersGrid');
            grid.innerHTML = '';
            
            numbers.forEach((numObj, index) => {
                const card = document.createElement('div');
                card.className = 'number-card';
                card.textContent = numObj.value;
                card.onclick = () => selectNumber(index, card);
                grid.appendChild(card);
            });
        }

        function selectNumber(index, cardElement) {
            if (!gameActive) return;
            
            const numObj = numbers[index];
            
            if (selectedNumbers.includes(index)) {
                // 取消選擇
                selectedNumbers = selectedNumbers.filter(i => i !== index);
                cardElement.classList.remove('selected');
            } else {
                // 選擇
                selectedNumbers.push(index);
                cardElement.classList.add('selected');
            }
        }

        function checkAnswer() {
            if (!gameActive) return;
            
            if (selectedNumbers.length === 0) {
                showMessage('請先選擇要分類的數字！', 'error');
                return;
            }
            
            let correct = 0;
            let total = selectedNumbers.length;
            
            selectedNumbers.forEach(index => {
                const numObj = numbers[index];
                const cardElement = document.querySelectorAll('.number-card')[index];
                
                if (numObj.type === 'factor') {
                    cardElement.classList.add('correct');
                    correct++;
                } else {
                    cardElement.classList.add('wrong');
                }
            });
            
            const accuracy = (correct / total) * 100;
            score += Math.round(accuracy);
            
            if (accuracy === 100) {
                showMessage('完美！所有分類都正確！', 'success');
                playSound('success');
                celebrateConfetti(100);
            } else {
                showMessage(`正確率: ${accuracy.toFixed(1)}%`, accuracy >= 80 ? 'success' : 'error');
                if (accuracy < 80) playSound('error');
            }
            
            updateScore();
            selectedNumbers = [];
        }

        function updateScore() {
            const high = getHighScore(HS_KEY);
            if (score > high) setHighScore(HS_KEY, score);
            document.getElementById('score').innerHTML = `得分: ${score} <span class=\"high-score\">(最高分：${getHighScore(HS_KEY)})</span>`;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            targetNumber = Math.floor(Math.random() * 20) + 10; // 10-30之間的隨機數
            numbers = generateNumbers();
            selectedNumbers = [];
            score = 0;
            
            document.getElementById('targetNumber').textContent = targetNumber;
            createNumbersGrid();
            updateScore();
            showMessage('將數字拖拽到正確的分類中！', 'success');
        }

        // 初始化遊戲
        newGame();
    </script>
</body>
</html>
