// Enhanced shared utilities: sound effects, confetti, storage helpers, animations

// Enhanced sound system with Web Audio API
let audioContext;
let soundEnabled = true;

export function initAudio() {
  if (!audioContext) {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  }
}

export function toggleSound() {
  soundEnabled = !soundEnabled;
  return soundEnabled;
}

export function playSound(type = 'click', volume = 0.3) {
  if (!soundEnabled) return;

  try {
    initAudio();

    // Create different tones for different sound types
    const frequencies = {
      click: [800, 0.1],
      success: [523, 0.3, 659, 0.2, 784, 0.3], // C-E-G chord
      error: [300, 0.2, 250, 0.3],
      collect: [1000, 0.1, 1200, 0.1],
      level_up: [440, 0.2, 554, 0.2, 659, 0.2, 880, 0.3],
      tick: [1500, 0.05],
      whoosh: [200, 0.1, 400, 0.1, 600, 0.1]
    };

    const soundData = frequencies[type] || frequencies.click;

    for (let i = 0; i < soundData.length; i += 2) {
      const frequency = soundData[i];
      const duration = soundData[i + 1];

      setTimeout(() => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        oscillator.type = type === 'error' ? 'sawtooth' : 'sine';

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
      }, i * 50);
    }
  } catch (error) {
    console.log('Audio not supported:', error);
  }
}

// Enhanced confetti system with multiple effects
export function celebrateConfetti(pieces = 80, type = 'default') {
  const containerId = 'confetti-container';
  let container = document.getElementById(containerId);
  if (!container) {
    container = document.createElement('div');
    container.id = containerId;
    container.className = 'confetti-container';
    document.body.appendChild(container);
  }

  const effects = {
    default: () => createDefaultConfetti(container, pieces),
    burst: () => createBurstConfetti(container, pieces),
    rain: () => createRainConfetti(container, pieces),
    spiral: () => createSpiralConfetti(container, pieces)
  };

  (effects[type] || effects.default)();
  playSound('success');
}

function createDefaultConfetti(container, pieces) {
  const colors = ['#ffd06b', '#8b91ff', '#ff91a4', '#43e97b', '#4facfe', '#f093fb', '#a8edea'];
  const shapes = ['square', 'circle', 'triangle'];

  for (let i = 0; i < pieces; i++) {
    const piece = document.createElement('div');
    piece.className = 'confetti-piece';

    const color = colors[Math.floor(Math.random() * colors.length)];
    const shape = shapes[Math.floor(Math.random() * shapes.length)];

    piece.style.background = color;
    piece.style.left = Math.random() * 100 + 'vw';
    piece.style.width = (Math.random() * 8 + 6) + 'px';
    piece.style.height = piece.style.width;

    if (shape === 'circle') {
      piece.style.borderRadius = '50%';
    } else if (shape === 'triangle') {
      piece.style.background = 'transparent';
      piece.style.borderLeft = '5px solid transparent';
      piece.style.borderRight = '5px solid transparent';
      piece.style.borderBottom = '10px solid ' + color;
      piece.style.width = '0';
      piece.style.height = '0';
    }

    const drift = (Math.random() * 60 - 30) + 'vw';
    piece.style.setProperty('--cx-end', drift);
    piece.style.animationDuration = (Math.random() * 2 + 2) + 's';
    piece.style.animationDelay = (Math.random() * 0.5) + 's';

    container.appendChild(piece);
    setTimeout(() => piece.remove(), 4000);
  }
}

function createBurstConfetti(container, pieces) {
  const colors = ['#ffd06b', '#8b91ff', '#ff91a4', '#43e97b'];
  const centerX = window.innerWidth / 2;
  const centerY = window.innerHeight / 2;

  for (let i = 0; i < pieces; i++) {
    const piece = document.createElement('div');
    piece.className = 'confetti-piece';
    piece.style.background = colors[Math.floor(Math.random() * colors.length)];
    piece.style.position = 'fixed';
    piece.style.left = centerX + 'px';
    piece.style.top = centerY + 'px';
    piece.style.width = '8px';
    piece.style.height = '8px';

    const angle = (i / pieces) * Math.PI * 2;
    const velocity = Math.random() * 200 + 100;
    const endX = centerX + Math.cos(angle) * velocity;
    const endY = centerY + Math.sin(angle) * velocity;

    piece.style.setProperty('--end-x', endX + 'px');
    piece.style.setProperty('--end-y', endY + 'px');

    piece.style.animation = `burstOut 1.5s ease-out forwards`;

    container.appendChild(piece);
    setTimeout(() => piece.remove(), 2000);
  }

  // Add burst animation keyframes if not exists
  if (!document.querySelector('#burst-keyframes')) {
    const style = document.createElement('style');
    style.id = 'burst-keyframes';
    style.textContent = `
      @keyframes burstOut {
        0% { transform: translate(0, 0) scale(1); opacity: 1; }
        100% { transform: translate(var(--end-x, 0), var(--end-y, 0)) scale(0); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  }
}

// Enhanced storage and utility functions
export function getHighScore(key) {
  try {
    return Number(localStorage.getItem(`highscore_${key}`) || 0);
  } catch (_) {
    return 0;
  }
}

export function setHighScore(key, value) {
  try {
    const current = getHighScore(key);
    if (value > current) {
      localStorage.setItem(`highscore_${key}`, String(value));
      return true; // New high score!
    }
    return false;
  } catch (_) {
    return false;
  }
}

export function getGameStats(key) {
  try {
    const stats = localStorage.getItem(`stats_${key}`);
    return stats ? JSON.parse(stats) : {
      gamesPlayed: 0,
      totalScore: 0,
      bestTime: 0,
      averageScore: 0
    };
  } catch (_) {
    return { gamesPlayed: 0, totalScore: 0, bestTime: 0, averageScore: 0 };
  }
}

export function updateGameStats(key, score, time = 0) {
  try {
    const stats = getGameStats(key);
    stats.gamesPlayed++;
    stats.totalScore += score;
    stats.averageScore = Math.round(stats.totalScore / stats.gamesPlayed);
    if (time > 0 && (stats.bestTime === 0 || time < stats.bestTime)) {
      stats.bestTime = time;
    }
    localStorage.setItem(`stats_${key}`, JSON.stringify(stats));
    return stats;
  } catch (_) {
    return null;
  }
}

export function formatTime(seconds) {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${String(m).padStart(2,'0')}:${String(s).padStart(2,'0')}`;
}

export function formatNumber(num) {
  return new Intl.NumberFormat('zh-TW').format(num);
}

// Animation utilities
export function animateElement(element, animationClass, duration = 500) {
  return new Promise((resolve) => {
    element.classList.add(animationClass);
    setTimeout(() => {
      element.classList.remove(animationClass);
      resolve();
    }, duration);
  });
}

export function createFloatingText(text, x, y, color = '#4ade80') {
  const floatingText = document.createElement('div');
  floatingText.textContent = text;
  floatingText.style.cssText = `
    position: fixed;
    left: ${x}px;
    top: ${y}px;
    color: ${color};
    font-weight: bold;
    font-size: 1.5rem;
    pointer-events: none;
    z-index: 9999;
    animation: floatUp 2s ease-out forwards;
  `;

  document.body.appendChild(floatingText);

  // Add floating animation if not exists
  if (!document.querySelector('#float-keyframes')) {
    const style = document.createElement('style');
    style.id = 'float-keyframes';
    style.textContent = `
      @keyframes floatUp {
        0% { transform: translateY(0) scale(1); opacity: 1; }
        100% { transform: translateY(-100px) scale(1.2); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  }

  setTimeout(() => floatingText.remove(), 2000);
}

// Particle effects
export function createParticleEffect(x, y, color = '#ffd06b', count = 10) {
  for (let i = 0; i < count; i++) {
    const particle = document.createElement('div');
    particle.style.cssText = `
      position: fixed;
      left: ${x}px;
      top: ${y}px;
      width: 4px;
      height: 4px;
      background: ${color};
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
    `;

    const angle = (i / count) * Math.PI * 2;
    const velocity = Math.random() * 50 + 30;
    const endX = x + Math.cos(angle) * velocity;
    const endY = y + Math.sin(angle) * velocity;

    particle.style.animation = `particleFloat 1s ease-out forwards`;
    particle.style.setProperty('--end-x', endX + 'px');
    particle.style.setProperty('--end-y', endY + 'px');

    document.body.appendChild(particle);
    setTimeout(() => particle.remove(), 1000);
  }

  // Add particle animation if not exists
  if (!document.querySelector('#particle-keyframes')) {
    const style = document.createElement('style');
    style.id = 'particle-keyframes';
    style.textContent = `
      @keyframes particleFloat {
        0% { transform: translate(0, 0) scale(1); opacity: 1; }
        100% { transform: translate(var(--end-x, 0), var(--end-y, 0)) scale(0); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  }
}

// Screen shake effect
export function shakeScreen(intensity = 10, duration = 500) {
  const body = document.body;
  body.style.animation = `shake ${duration}ms ease-in-out`;
  body.style.setProperty('--shake-intensity', intensity + 'px');

  setTimeout(() => {
    body.style.animation = '';
  }, duration);

  // Add shake animation if not exists
  if (!document.querySelector('#shake-screen-keyframes')) {
    const style = document.createElement('style');
    style.id = 'shake-screen-keyframes';
    style.textContent = `
      @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10% { transform: translateX(var(--shake-intensity, 10px)); }
        20% { transform: translateX(calc(-1 * var(--shake-intensity, 10px))); }
        30% { transform: translateX(var(--shake-intensity, 10px)); }
        40% { transform: translateX(calc(-1 * var(--shake-intensity, 10px))); }
        50% { transform: translateX(var(--shake-intensity, 10px)); }
        60% { transform: translateX(calc(-1 * var(--shake-intensity, 10px))); }
        70% { transform: translateX(var(--shake-intensity, 10px)); }
        80% { transform: translateX(calc(-1 * var(--shake-intensity, 10px))); }
        90% { transform: translateX(var(--shake-intensity, 10px)); }
      }
    `;
    document.head.appendChild(style);
  }
}


