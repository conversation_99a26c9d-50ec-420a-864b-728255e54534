// Shared cute utilities: sound effects, confetti, storage helpers

export function playSound(type = 'click') {
  const sounds = {
    click: 'data:audio/mp3;base64,//uQZAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAACcQAA',
    success: 'data:audio/mp3;base64,//uQZAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAACcQAA',
    error: 'data:audio/mp3;base64,//uQZAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAACcQAA'
  };
  const src = sounds[type] || sounds.click;
  const audio = new Audio(src);
  audio.volume = 0.3;
  audio.play().catch(() => {});
}

export function celebrateConfetti(pieces = 80) {
  const containerId = 'confetti-container';
  let container = document.getElementById(containerId);
  if (!container) {
    container = document.createElement('div');
    container.id = containerId;
    container.className = 'confetti-container';
    document.body.appendChild(container);
  }
  for (let i = 0; i < pieces; i++) {
    const piece = document.createElement('div');
    piece.className = 'confetti-piece';
    const colors = ['#ffd06b', '#8b91ff', '#ff91a4', '#43e97b', '#4facfe'];
    piece.style.background = colors[Math.floor(Math.random() * colors.length)];
    piece.style.left = Math.random() * 100 + 'vw';
    const drift = (Math.random() * 40 - 20) + 'vw';
    piece.style.setProperty('--cx-end', drift);
    piece.style.animationDuration = (Math.random() * 1.5 + 1.5) + 's';
    container.appendChild(piece);
    setTimeout(() => piece.remove(), 3000);
  }
}

export function getHighScore(key) {
  try {
    return Number(localStorage.getItem(key) || 0);
  } catch (_) {
    return 0;
  }
}

export function setHighScore(key, value) {
  try {
    const current = getHighScore(key);
    if (value > current) localStorage.setItem(key, String(value));
  } catch (_) {}
}

export function formatTime(seconds) {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${String(m).padStart(2,'0')}:${String(s).padStart(2,'0')}`;
}


