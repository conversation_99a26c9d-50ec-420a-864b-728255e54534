<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數與倍數數學遊戲集合</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 3em;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.2em;
        }
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .game-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .game-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }
        .game-card:nth-child(2n) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .game-card:nth-child(3n) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .game-card:nth-child(4n) {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .game-card:nth-child(5n) {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .game-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .game-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .game-description {
            font-size: 0.9em;
            opacity: 0.9;
            line-height: 1.4;
        }
        .intro-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            text-align: center;
        }
        .intro-section h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .intro-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .feature {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .feature h3 {
            color: #27ae60;
            margin-bottom: 10px;
        }
        .feature p {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body class="cute-container">
    <div class="container">
        <h1>🎮 因數與倍數數學遊戲集合</h1>
        <p class="subtitle">讓小學生在遊戲中快樂學習數學概念</p>
        
        <div class="intro-section">
            <h2>🌟 歡迎來到數學遊戲世界！</h2>
            <p>這是一個專為小學生設計的因數與倍數學習遊戲集合，透過有趣的互動遊戲，讓孩子們在玩樂中掌握數學概念。</p>
            <p>每個遊戲都經過精心設計，結合了教育性和趣味性，幫助學生更好地理解因數與倍數的關係。</p>
        </div>
        
        <div class="games-grid">
            <a href="因數倍數拖拉配對.html" class="game-card">
                <div class="game-icon">🧲</div>
                <div class="game-title">因數倍數拖拉配對</div>
                <div class="game-description">把數字拖進正確區域，練習因數與倍數的判斷！</div>
            </a>
            <a href="因數與倍數選擇題.html" class="game-card">
                <div class="game-icon">🧮</div>
                <div class="game-title">因數與倍數選擇題</div>
                <div class="game-description">多樣化題型＋難度選擇，搭配計分計時與錯題解析！</div>
            </a>
            <a href="因數獵人遊戲.html" class="game-card">
                <div class="game-icon">🔍</div>
                <div class="game-title">因數獵人遊戲</div>
                <div class="game-description">在時間限制內找出指定數字的所有因數，挑戰你的因數分解能力！</div>
            </a>
            
            <a href="倍數跳格子遊戲.html" class="game-card">
                <div class="game-icon">🦘</div>
                <div class="game-title">倍數跳格子遊戲</div>
                <div class="game-description">點擊正確的倍數格子，在跳躍中學習倍數概念！</div>
            </a>
            
            <a href="因數配對記憶卡遊戲.html" class="game-card">
                <div class="game-icon">🧠</div>
                <div class="game-title">因數配對記憶卡遊戲</div>
                <div class="game-description">翻開卡片找到因數配對，訓練記憶力的同時學習因數關係！</div>
            </a>
            
            <a href="倍數接龍遊戲.html" class="game-card">
                <div class="game-icon">🔗</div>
                <div class="game-title">倍數接龍遊戲</div>
                <div class="game-description">按順序說出倍數，在接龍中熟悉倍數序列！</div>
            </a>
            
            <a href="因數樹建構遊戲.html" class="game-card">
                <div class="game-icon">🌳</div>
                <div class="game-title">因數樹建構遊戲</div>
                <div class="game-description">分解數字成質因數，建構屬於你的因數樹！</div>
            </a>
            
            <a href="倍數賓果遊戲.html" class="game-card">
                <div class="game-icon">🎯</div>
                <div class="game-title">倍數賓果遊戲</div>
                <div class="game-description">標記倍數數字，在賓果遊戲中學習倍數識別！</div>
            </a>
            
            <a href="因數大富翁遊戲.html" class="game-card">
                <div class="game-icon">🎲</div>
                <div class="game-title">因數大富翁遊戲</div>
                <div class="game-description">移動並回答因數問題，在遊戲板上學習因數知識！</div>
            </a>
            
            <a href="倍數時鐘遊戲.html" class="game-card">
                <div class="game-icon">🕐</div>
                <div class="game-title">倍數時鐘遊戲</div>
                <div class="game-description">用時鐘學習倍數，將數學與時間概念結合！</div>
            </a>
            
            <a href="因數分類競賽遊戲.html" class="game-card">
                <div class="game-icon">🏆</div>
                <div class="game-title">因數分類競賽遊戲</div>
                <div class="game-description">按因數關係分類數字，在競賽中提升分類能力！</div>
            </a>
            
            <a href="倍數音樂椅遊戲.html" class="game-card">
                <div class="game-icon">🎵</div>
                <div class="game-title">倍數音樂椅遊戲</div>
                <div class="game-description">音樂停止時點擊正確椅子，在動態環境中練習倍數識別！</div>
            </a>
            
            <a href="因數打地鼠遊戲.html" class="game-card">
                <div class="game-icon">🐹</div>
                <div class="game-title">因數打地鼠遊戲</div>
                <div class="game-description">快速點擊因數地鼠，在時間限制內獲得最高分數！</div>
            </a>
            
            <a href="倍數快跑遊戲.html" class="game-card">
                <div class="game-icon">🏃‍♂️</div>
                <div class="game-title">倍數快跑遊戲</div>
                <div class="game-description">小角色在道路上奔跑，只能吃到目標數字倍數的道具！</div>
            </a>
            
            <a href="因數迷宮遊戲.html" class="game-card">
                <div class="game-icon">🧩</div>
                <div class="game-title">因數迷宮遊戲</div>
                <div class="game-description">在迷宮中尋找出口，遇到數字門時輸入正確的因數才能通過！</div>
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <h3>針對性學習</h3>
                <p>每個遊戲都專注於特定的數學概念，幫助學生深入理解因數與倍數</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🎮</div>
                <h3>互動式體驗</h3>
                <p>豐富的互動元素和即時反饋，讓學習過程更加有趣和有效</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>進度追蹤</h3>
                <p>內建計分系統，幫助學生和老師追蹤學習進度和掌握程度</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🌟</div>
                <h3>多樣化設計</h3>
                <p>十二種不同的遊戲模式，滿足不同學習風格和興趣偏好</p>
            </div>
        </div>
        
        <div class="intro-section">
            <h2>🚀 開始你的數學冒險！</h2>
            <p>點擊上方任意遊戲卡片，開始你的因數與倍數學習之旅。每個遊戲都設計得簡單易懂，適合小學生獨立操作。</p>
            <p>建議按照順序體驗所有遊戲，這樣可以全面掌握因數與倍數的各種概念和應用。</p>
        </div>
    </div>
</body>
</html>
