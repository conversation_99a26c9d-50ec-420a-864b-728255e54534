<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數與倍數數學遊戲集合</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background elements */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 50px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            position: relative;
            animation: containerFadeIn 1s ease-out;
        }

        @keyframes containerFadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        h1 {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            font-size: 3.5em;
            font-weight: 900;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            animation: titleGlow 3s ease-in-out infinite alternate;
            position: relative;
        }

        @keyframes titleGlow {
            0% { filter: brightness(1) saturate(1); }
            100% { filter: brightness(1.1) saturate(1.2); }
        }

        h1::after {
            content: '✨';
            position: absolute;
            top: -10px;
            right: -20px;
            font-size: 0.5em;
            animation: sparkle 2s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.7; }
            50% { transform: rotate(180deg) scale(1.2); opacity: 1; }
        }

        .subtitle {
            text-align: center;
            color: #555;
            margin-bottom: 50px;
            font-size: 1.3em;
            font-weight: 500;
            animation: subtitleSlide 1s ease-out 0.3s both;
        }

        @keyframes subtitleSlide {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 50px 0;
            animation: gridFadeIn 1s ease-out 0.6s both;
        }

        @keyframes gridFadeIn {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .game-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            color: white;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
            transition: left 0.6s ease;
            z-index: 1;
        }

        .game-card:hover::before {
            left: 100%;
        }

        .game-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0,0,0,0.25);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .game-card:active {
            transform: translateY(-4px) scale(0.98);
        }
        .game-card:nth-child(2n) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .game-card:nth-child(3n) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .game-card:nth-child(4n) {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .game-card:nth-child(5n) {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .game-icon {
            font-size: 3.5em;
            margin-bottom: 20px;
            display: block;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .game-card:hover .game-icon {
            transform: scale(1.1) rotate(5deg);
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        }

        .game-title {
            font-size: 1.4em;
            font-weight: 900;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .game-description {
            font-size: 1em;
            opacity: 0.95;
            line-height: 1.5;
            position: relative;
            z-index: 2;
            font-weight: 500;
        }
        .intro-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid rgba(139, 145, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
            animation: sectionSlide 1s ease-out 0.9s both;
        }

        .intro-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(139, 145, 255, 0.05) 0%, transparent 70%);
            animation: sectionGlow 4s ease-in-out infinite;
            z-index: 0;
        }

        @keyframes sectionGlow {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }

        @keyframes sectionSlide {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .intro-section h2 {
            color: #334155;
            margin-bottom: 25px;
            font-size: 2em;
            font-weight: 900;
            position: relative;
            z-index: 1;
        }

        .intro-section p {
            color: #64748b;
            line-height: 1.7;
            margin-bottom: 20px;
            font-size: 1.1em;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .feature {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .feature h3 {
            color: #27ae60;
            margin-bottom: 10px;
        }
        .feature p {
            color: #666;
            font-size: 0.9em;
        }
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 30px 20px;
            }

            h1 {
                font-size: 2.5em;
            }

            .games-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .game-card {
                padding: 25px 20px;
            }

            .intro-section, .features {
                padding: 30px 20px;
            }
        }

        /* Sound toggle button */
        .sound-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .sound-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .sound-toggle.muted {
            background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
        }
    </style>
</head>
<body class="cute-container">
    <div class="container">
        <h1>🎮 因數與倍數數學遊戲集合</h1>
        <p class="subtitle">讓小學生在遊戲中快樂學習數學概念</p>
        
        <div class="intro-section">
            <h2>🌟 歡迎來到數學遊戲世界！</h2>
            <p>這是一個專為小學生設計的因數與倍數學習遊戲集合，透過有趣的互動遊戲，讓孩子們在玩樂中掌握數學概念。</p>
            <p>每個遊戲都經過精心設計，結合了教育性和趣味性，幫助學生更好地理解因數與倍數的關係。</p>
        </div>
        
        <div class="games-grid">
            <a href="因數倍數拖拉配對.html" class="game-card">
                <div class="game-icon">🧲</div>
                <div class="game-title">因數倍數拖拉配對</div>
                <div class="game-description">把數字拖進正確區域，練習因數與倍數的判斷！</div>
            </a>
            <a href="因數與倍數選擇題.html" class="game-card">
                <div class="game-icon">🧮</div>
                <div class="game-title">因數與倍數選擇題</div>
                <div class="game-description">多樣化題型＋難度選擇，搭配計分計時與錯題解析！</div>
            </a>
            <a href="因數獵人遊戲.html" class="game-card">
                <div class="game-icon">🔍</div>
                <div class="game-title">因數獵人遊戲</div>
                <div class="game-description">在時間限制內找出指定數字的所有因數，挑戰你的因數分解能力！</div>
            </a>
            
            <a href="倍數跳格子遊戲.html" class="game-card">
                <div class="game-icon">🦘</div>
                <div class="game-title">倍數跳格子遊戲</div>
                <div class="game-description">點擊正確的倍數格子，在跳躍中學習倍數概念！</div>
            </a>
            
            <a href="因數配對記憶卡遊戲.html" class="game-card">
                <div class="game-icon">🧠</div>
                <div class="game-title">因數配對記憶卡遊戲</div>
                <div class="game-description">翻開卡片找到因數配對，訓練記憶力的同時學習因數關係！</div>
            </a>
            
            <a href="倍數接龍遊戲.html" class="game-card">
                <div class="game-icon">🔗</div>
                <div class="game-title">倍數接龍遊戲</div>
                <div class="game-description">按順序說出倍數，在接龍中熟悉倍數序列！</div>
            </a>
            
            <a href="因數樹建構遊戲.html" class="game-card">
                <div class="game-icon">🌳</div>
                <div class="game-title">因數樹建構遊戲</div>
                <div class="game-description">分解數字成質因數，建構屬於你的因數樹！</div>
            </a>
            
            <a href="倍數賓果遊戲.html" class="game-card">
                <div class="game-icon">🎯</div>
                <div class="game-title">倍數賓果遊戲</div>
                <div class="game-description">標記倍數數字，在賓果遊戲中學習倍數識別！</div>
            </a>
            
            <a href="因數大富翁遊戲.html" class="game-card">
                <div class="game-icon">🎲</div>
                <div class="game-title">因數大富翁遊戲</div>
                <div class="game-description">移動並回答因數問題，在遊戲板上學習因數知識！</div>
            </a>
            
            <a href="倍數時鐘遊戲.html" class="game-card">
                <div class="game-icon">🕐</div>
                <div class="game-title">倍數時鐘遊戲</div>
                <div class="game-description">用時鐘學習倍數，將數學與時間概念結合！</div>
            </a>
            
            <a href="因數分類競賽遊戲.html" class="game-card">
                <div class="game-icon">🏆</div>
                <div class="game-title">因數分類競賽遊戲</div>
                <div class="game-description">按因數關係分類數字，在競賽中提升分類能力！</div>
            </a>
            
            <a href="倍數音樂椅遊戲.html" class="game-card">
                <div class="game-icon">🎵</div>
                <div class="game-title">倍數音樂椅遊戲</div>
                <div class="game-description">音樂停止時點擊正確椅子，在動態環境中練習倍數識別！</div>
            </a>
            
            <a href="因數打地鼠遊戲.html" class="game-card">
                <div class="game-icon">🐹</div>
                <div class="game-title">因數打地鼠遊戲</div>
                <div class="game-description">快速點擊因數地鼠，在時間限制內獲得最高分數！</div>
            </a>
            
            <a href="倍數快跑遊戲.html" class="game-card">
                <div class="game-icon">🏃‍♂️</div>
                <div class="game-title">倍數快跑遊戲</div>
                <div class="game-description">小角色在道路上奔跑，只能吃到目標數字倍數的道具！</div>
            </a>
            
            <a href="因數迷宮遊戲.html" class="game-card">
                <div class="game-icon">🧩</div>
                <div class="game-title">因數迷宮遊戲</div>
                <div class="game-description">在迷宮中尋找出口，遇到數字門時輸入正確的因數才能通過！</div>
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <h3>針對性學習</h3>
                <p>每個遊戲都專注於特定的數學概念，幫助學生深入理解因數與倍數</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🎮</div>
                <h3>互動式體驗</h3>
                <p>豐富的互動元素和即時反饋，讓學習過程更加有趣和有效</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📊</div>
                <h3>進度追蹤</h3>
                <p>內建計分系統，幫助學生和老師追蹤學習進度和掌握程度</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🌟</div>
                <h3>多樣化設計</h3>
                <p>十二種不同的遊戲模式，滿足不同學習風格和興趣偏好</p>
            </div>
        </div>
        
        <div class="intro-section">
            <h2>🚀 開始你的數學冒險！</h2>
            <p>點擊上方任意遊戲卡片，開始你的因數與倍數學習之旅。每個遊戲都設計得簡單易懂，適合小學生獨立操作。</p>
            <p>建議按照順序體驗所有遊戲，這樣可以全面掌握因數與倍數的各種概念和應用。</p>
        </div>
    </div>

    <!-- Sound toggle button -->
    <button class="sound-toggle" id="soundToggle" title="切換音效">🔊</button>

    <script type="module">
        import { playSound, toggleSound, celebrateConfetti, initAudio } from './game-utils.js';

        // Initialize audio on first user interaction
        let audioInitialized = false;

        function initializeAudio() {
            if (!audioInitialized) {
                initAudio();
                audioInitialized = true;
            }
        }

        // Sound toggle functionality
        const soundToggle = document.getElementById('soundToggle');
        let soundEnabled = true;

        soundToggle.addEventListener('click', () => {
            initializeAudio();
            soundEnabled = toggleSound();
            soundToggle.textContent = soundEnabled ? '🔊' : '🔇';
            soundToggle.classList.toggle('muted', !soundEnabled);
            playSound('click');
        });

        // Add hover effects and sounds to game cards
        const gameCards = document.querySelectorAll('.game-card');

        gameCards.forEach((card, index) => {
            // Stagger animation delays
            card.style.animationDelay = `${0.1 * index}s`;

            card.addEventListener('mouseenter', () => {
                initializeAudio();
                playSound('tick', 0.1);
            });

            card.addEventListener('click', (e) => {
                initializeAudio();
                playSound('click');

                // Add click effect
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: absolute;
                    left: ${x}px;
                    top: ${y}px;
                    width: 0;
                    height: 0;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.5);
                    transform: translate(-50%, -50%);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                    z-index: 10;
                `;

                card.style.position = 'relative';
                card.appendChild(ripple);

                setTimeout(() => ripple.remove(), 600);
            });
        });

        // Add ripple animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                0% { width: 0; height: 0; opacity: 1; }
                100% { width: 200px; height: 200px; opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Welcome confetti after page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                celebrateConfetti(50, 'default');
            }, 1500);
        });

        // Add parallax effect to background
        let ticking = false;

        function updateParallax() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('body::before');

            if (parallax) {
                const yPos = -(scrolled * 0.5);
                parallax.style.transform = `translate3d(0, ${yPos}px, 0)`;
            }

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fadeIn');
                }
            });
        }, observerOptions);

        // Observe all sections
        document.querySelectorAll('.intro-section, .features, .game-card').forEach(el => {
            observer.observe(el);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                const focused = document.activeElement;
                if (focused.classList.contains('game-card')) {
                    e.preventDefault();
                    focused.click();
                }
            }
        });

        // Performance optimization: reduce animations on low-end devices
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            document.documentElement.style.setProperty('--transition-normal', '0.15s');
            document.documentElement.style.setProperty('--transition-slow', '0.25s');
        }
    </script>
</body>
</html>
