<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數倍數拖拉配對</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .page { max-width: 980px; margin: 0 auto; padding: 24px; }
        .card { background:#fff; border-radius: 18px; padding: 20px; box-shadow: 0 10px 24px rgba(0,0,0,0.12); }
        .toolbar { display:flex; flex-wrap:wrap; gap:10px; align-items:center; justify-content: space-between; margin-bottom: 12px; }
        .board { display:grid; grid-template-columns: 1fr 1fr; gap: 14px; margin-top: 8px; }
        .dropzone { min-height: 180px; border: 3px dashed #e0e0ff; border-radius: 14px; padding: 12px; background: #fafaff; }
        .zone-title { font-weight: 900; margin-bottom: 8px; }
        .chips { display:flex; flex-wrap:wrap; gap: 10px; }
        .draggable { background:#fff; border:2px solid #e9e9ff; border-radius: 12px; padding: 10px 12px; font-weight: 800; cursor: grab; user-select: none; }
        .draggable.dragging { opacity: .6; }
        .dropzone.over { background:#f3f6ff; }
        .palette { display:flex; flex-wrap:wrap; gap: 10px; margin-top: 12px; }
        .footer-bar { display:flex; justify-content: space-between; align-items:center; margin-top: 12px; }
        .img-wrap { text-align:center; margin-top: 10px; }
        .img-wrap img { width: 120px; height: 120px; object-fit: contain; }
    </style>
</head>
<body class="cute-container">
  <div class="page">
    <div style="display:flex; align-items:center; gap:10px; justify-content: space-between; margin-bottom: 12px;">
      <a class="back-btn" href="index.html">← 返回遊戲集合</a>
      <div class="high-score" id="highScoreBox">🏆 最高分：0</div>
    </div>

    <div class="card pop">
      <h1>🧲 因數倍數拖拉配對</h1>
      <div class="toolbar">
        <div class="status">
          <span class="badge">回合：<span id="round">1</span></span>
          <span class="badge">分數：<span id="score">0</span></span>
          <span class="badge">時間：<span id="time">00:00</span></span>
          <span class="badge">❤️ <span id="lives">3</span></span>
        </div>
        <div class="status">
          <select id="difficulty" class="btn btn-accent">
            <option value="easy">簡單 (1-10)</option>
            <option value="medium">中等 (1-50)</option>
            <option value="hard">困難 (1-100)</option>
          </select>
          <select id="theme" class="btn btn-secondary">
            <option value="forest">森林動物</option>
            <option value="space">太空探險</option>
            <option value="ocean">海洋世界</option>
            <option value="cartoon">卡通人物</option>
          </select>
          <button id="startBtn" class="btn btn-primary">開始</button>
        </div>
      </div>

      <div class="pill" id="task">任務：—</div>
      <div class="img-wrap"><img id="qImg" alt="cute" src="" style="display:none"></div>

      <div class="board">
        <div class="dropzone" id="zoneA">
          <div class="zone-title" id="zoneATitle">拖到這裡：是因數</div>
          <div class="chips" id="chipsA"></div>
        </div>
        <div class="dropzone" id="zoneB">
          <div class="zone-title" id="zoneBTitle">拖到這裡：不是因數</div>
          <div class="chips" id="chipsB"></div>
        </div>
      </div>

      <div>
        <div class="palette" id="palette"></div>
      </div>

      <div class="footer-bar">
        <div id="msg"></div>
        <div>
          <button id="checkBtn" class="btn">檢查</button>
          <button id="skipBtn" class="btn">跳過</button>
          <button id="saveBtn" class="btn">保存進度</button>
        </div>
      </div>
    </div>

    <div class="card" style="margin-top: 16px;">
      <h3>📝 錯題回顧</h3>
      <div id="review"></div>
    </div>
  </div>

  <script type="module">
    import { playSound, celebrateConfetti, formatTime, getHighScore, setHighScore } from './game-utils.js';

    const els = {
      round: document.getElementById('round'),
      score: document.getElementById('score'),
      time: document.getElementById('time'),
      lives: document.getElementById('lives'),
      difficulty: document.getElementById('difficulty'),
      theme: document.getElementById('theme'),
      startBtn: document.getElementById('startBtn'),
      checkBtn: document.getElementById('checkBtn'),
      skipBtn: document.getElementById('skipBtn'),
      saveBtn: document.getElementById('saveBtn'),
      palette: document.getElementById('palette'),
      zoneA: document.getElementById('zoneA'),
      zoneB: document.getElementById('zoneB'),
      chipsA: document.getElementById('chipsA'),
      chipsB: document.getElementById('chipsB'),
      task: document.getElementById('task'),
      review: document.getElementById('review'),
      msg: document.getElementById('msg'),
      qImg: document.getElementById('qImg'),
      zoneATitle: document.getElementById('zoneATitle'),
      zoneBTitle: document.getElementById('zoneBTitle'),
      highScoreBox: document.getElementById('highScoreBox')
    };

    const HS_KEY = 'dnd_factors_multiples_highscore_v1';
    const SAVE_KEY = 'dnd_factors_multiples_progress_v1';
    els.highScoreBox.textContent = `🏆 最高分：${getHighScore(HS_KEY)}`;

    let state = { round: 1, score: 0, lives: 3, seconds: 0, running: false, target: 0, type: '因數', pool: [], placedA: [], placedB: [], wrongList: [] };
    let timer = null;

    function rangeByDifficulty(diff) { if (diff==='easy') return 10; if (diff==='medium') return 50; return 100; }
    function randInt(n){ return 1 + Math.floor(Math.random()*n); }
    function pick(arr){ return arr[Math.floor(Math.random()*arr.length)]; }
    function getCuteImg(){
      const map = {
        forest:['https://unpkg.com/emoji-datasource-apple/img/apple/64/1f43c.png','https://unpkg.com/emoji-datasource-apple/img/apple/64/1f43f-fe0f.png'],
        space:['https://unpkg.com/emoji-datasource-apple/img/apple/64/1f680.png','https://unpkg.com/emoji-datasource-apple/img/apple/64/1f47d.png'],
        ocean:['https://unpkg.com/emoji-datasource-apple/img/apple/64/1f433.png','https://unpkg.com/emoji-datasource-apple/img/apple/64/1f42c.png'],
        cartoon:['https://unpkg.com/emoji-datasource-apple/img/apple/64/1f60a.png','https://unpkg.com/emoji-datasource-apple/img/apple/64/1f61c.png']
      };
      return pick(map[els.theme.value] || map.forest);
    }

    function generateRound(){
      const max = rangeByDifficulty(els.difficulty.value);
      const type = Math.random()<0.5 ? '因數' : '倍數';
      const target = randInt(max);
      const pool = new Set();
      while (pool.size < 8){
        const n = randInt(max*2);
        pool.add(n);
      }
      const numbers = Array.from(pool);
      state.type = type; state.target = target; state.pool = numbers; state.placedA = []; state.placedB = [];
      els.task.textContent = type==='因數' ? `任務：把正確的數字拖到「是 ${target} 的因數」` : `任務：把正確的數字拖到「是 ${target} 的倍數」`;
      els.zoneATitle.textContent = type==='因數' ? `拖到這裡：是 ${target} 的因數` : `拖到這裡：是 ${target} 的倍數`;
      els.zoneBTitle.textContent = type==='因數' ? `拖到這裡：不是 ${target} 的因數` : `拖到這裡：不是 ${target} 的倍數`;
      const img = getCuteImg(); els.qImg.src = img; els.qImg.style.display='inline-block';
      renderPalette();
    }

    function isCorrectA(n){ return state.type==='因數' ? (state.target % n === 0) : (n % state.target === 0); }

    function renderPalette(){
      els.palette.innerHTML = '';
      state.pool.forEach(n => {
        const chip = document.createElement('div');
        chip.className = 'draggable';
        chip.draggable = true;
        chip.textContent = String(n);
        chip.addEventListener('dragstart', e => { chip.classList.add('dragging'); e.dataTransfer.setData('text/plain', String(n)); playSound('click'); });
        chip.addEventListener('dragend', () => chip.classList.remove('dragging'));
        els.palette.appendChild(chip);
      });
    }

    function setupDropzone(zoneEl, listKey){
      zoneEl.addEventListener('dragover', e => { e.preventDefault(); zoneEl.classList.add('over'); });
      zoneEl.addEventListener('dragleave', () => zoneEl.classList.remove('over'));
      zoneEl.addEventListener('drop', e => {
        e.preventDefault(); zoneEl.classList.remove('over');
        const txt = e.dataTransfer.getData('text/plain');
        const n = Number(txt);
        const src = Array.from(els.palette.children).find(ch => ch.textContent === String(n));
        if (src){
          const chip = src.cloneNode(true);
          chip.draggable = false; chip.classList.remove('dragging');
          zoneEl.querySelector('.chips').appendChild(chip);
          els.palette.removeChild(src);
          (state[listKey]).push(n);
          playSound('click');
        }
      });
    }

    function updateHUD(){
      els.round.textContent = state.round;
      els.score.textContent = state.score;
      els.lives.textContent = state.lives;
      els.time.textContent = formatTime(state.seconds);
      els.highScoreBox.textContent = `🏆 最高分：${getHighScore(HS_KEY)}`;
    }

    function checkAnswer(){
      let correctCount = 0; let wrongMoves = 0;
      state.placedA.forEach(n => { if (isCorrectA(n)) correctCount++; else wrongMoves++; });
      state.placedB.forEach(n => { if (!isCorrectA(n)) correctCount++; else wrongMoves++; });
      const total = state.pool.length;
      const gain = Math.max(0, Math.floor((correctCount/total)*100) - wrongMoves*5);
      state.score += gain;
      if (wrongMoves>0){ state.lives -= 1; }
      if (wrongMoves===0){ celebrateConfetti(50); playSound('success'); els.msg.textContent = `✅ 完美！+${gain} 分`; }
      else { playSound('error'); els.msg.textContent = `部分錯誤：+${gain} 分（-1 生命）`; }

      // build review
      const roundWrong = [];
      state.pool.forEach(n => {
        const inA = state.placedA.includes(n);
        const shouldA = isCorrectA(n);
        if (inA !== shouldA){
          const why = state.type==='因數' ? (shouldA? `${n} 能整除 ${state.target}` : `${n} 不能整除 ${state.target}`) : (shouldA? `${n} 是 ${state.target} 的整數倍` : `${n} 不是 ${state.target} 的整數倍`);
          roundWrong.push({ n, why });
        }
      });
      if (roundWrong.length){ state.wrongList.push({ round: state.round, type: state.type, target: state.target, items: roundWrong }); }

      updateHUD();
      if (state.lives<=0) return endGame();
      state.round += 1; generateRound(); updateHUD();
    }

    function renderReview(){
      if (!state.wrongList.length){ els.review.textContent = '太棒了！沒有錯題～'; return; }
      els.review.innerHTML = '';
      state.wrongList.forEach(r => {
        const div = document.createElement('div');
        div.className = 'badge';
        const lines = r.items.map(it => `${it.n}：${it.why}`).join('，');
        div.textContent = `回合 ${r.round}（${r.type}，目標 ${r.target}）：${lines}`;
        els.review.appendChild(div);
      });
    }

    function tick(){ state.seconds += 1; els.time.textContent = formatTime(state.seconds); }
    function startGame(){
      state = { round:1, score:0, lives:3, seconds:0, running:true, target:0, type:'因數', pool:[], placedA:[], placedB:[], wrongList:[] };
      els.msg.textContent = '';
      clearInterval(timer); timer = setInterval(tick, 1000);
      generateRound(); updateHUD(); playSound('click');
    }
    function endGame(){
      state.running = false; clearInterval(timer);
      setHighScore(HS_KEY, state.score); updateHUD();
      els.msg.innerHTML = `🎉 遊戲結束！本次得分 ${state.score} 分。`;
      renderReview();
    }

    function saveProgress(){
      const payload = { state, difficulty: els.difficulty.value, theme: els.theme.value };
      try { localStorage.setItem(SAVE_KEY, JSON.stringify(payload)); els.msg.textContent = '✅ 已保存進度'; } catch (_) {}
    }
    function loadProgress(){
      try { const raw = localStorage.getItem(SAVE_KEY); if(!raw) return; const { state: saved, difficulty, theme } = JSON.parse(raw);
        els.difficulty.value = difficulty || 'easy'; els.theme.value = theme || 'forest';
        state = { ...saved, running:false }; updateHUD(); generateRound(); els.msg.textContent = '📦 已載入上次進度，按「開始」繼續'; } catch(_) {}
    }

    setupDropzone(els.zoneA, 'placedA');
    setupDropzone(els.zoneB, 'placedB');
    els.startBtn.addEventListener('click', startGame);
    els.checkBtn.addEventListener('click', () => { if(state.running) checkAnswer(); });
    els.skipBtn.addEventListener('click', () => { if(state.running){ state.round+=1; generateRound(); els.msg.textContent='⏭️ 已跳過'; updateHUD(); }});
    els.saveBtn.addEventListener('click', saveProgress);
    window.addEventListener('beforeunload', saveProgress);
    loadProgress();
  </script>
</body>
</html>

