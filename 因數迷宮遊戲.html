<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數迷宮遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .game-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .game-title {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: bold;
        }

        .game-description {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 20px;
        }

        .game-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 10px 20px;
            border-radius: 10px;
            font-weight: bold;
        }

        .game-area {
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }

        .maze-container {
            flex: 1;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            min-height: 500px;
        }

        .maze-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 2px;
            background: #ddd;
            padding: 10px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
            aspect-ratio: 10/8;
        }

        .maze-cell {
            background: #fff;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .maze-cell.wall {
            background: #333;
            cursor: not-allowed;
        }

        .maze-cell.path {
            background: #fff;
        }

        .maze-cell.door {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .maze-cell.door:hover {
            transform: scale(1.05);
        }

        .maze-cell.player {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            font-size: 1.5em;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 10;
        }

        .maze-cell.goal {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            font-size: 1.5em;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .controls-panel {
            width: 300px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .movement-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            max-width: 120px;
            margin: 0 auto;
        }

        .movement-btn {
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .movement-btn:hover {
            background: #7b7fff;
            transform: translateY(-2px);
        }

        .movement-btn:active {
            transform: translateY(0);
        }

        .hint-button {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            margin: 15px 0 10px 0;
            width: 100%;
            transition: all 0.3s ease;
            font-size: 1em;
        }

        .hint-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
        }

        .path-hint {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .door-question {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            text-align: center;
        }

        .door-number {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .answer-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1.2em;
            text-align: center;
            margin-bottom: 10px;
        }

        .answer-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .submit-btn {
            width: 100%;
            background: var(--success);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .submit-btn:hover {
            background: #27ae60;
        }

        .submit-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .feedback {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback.incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .instructions {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .instructions h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .instructions p {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .back-btn {
            background: #95a5a6;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 10px;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .victory-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
        }

        .victory-message.show {
            display: block;
            animation: popInCute 0.5s ease;
        }

        .victory-message h2 {
            color: #27ae60;
            margin-bottom: 20px;
            font-size: 2em;
        }

        .victory-message p {
            color: #666;
            margin-bottom: 20px;
            font-size: 1.2em;
        }

        .restart-btn {
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .restart-btn:hover {
            background: #7b7fff;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .game-area {
                flex-direction: column;
            }
            
            .controls-panel {
                width: 100%;
            }
            
            .maze-grid {
                max-width: 100%;
            }
        }
    </style>
</head>
<body class="cute-container">
    <div class="container">
        <a href="index.html" class="back-btn">← 返回遊戲選單</a>
        
        <div class="game-header">
            <h1 class="game-title">🧩 因數迷宮遊戲</h1>
            <p class="game-description">在迷宮中尋找出口，遇到數字門時輸入正確的因數才能通過！</p>
            
            <div class="game-stats">
                <div class="stat-item">🚪 通過門數: <span id="doors-passed">0</span></div>
                <div class="stat-item">⏱️ 時間: <span id="game-time">00:00</span></div>
                <div class="stat-item">🏆 最高分: <span id="high-score">0</span></div>
            </div>
        </div>

        <div class="game-area">
            <div class="maze-container">
                <div class="maze-grid" id="maze-grid">
                    <!-- 迷宮格子將由JavaScript動態生成 -->
                </div>
            </div>

            <div class="controls-panel">
                <div class="instructions">
                    <h4>🎮 遊戲說明</h4>
                    <p>• 使用方向鍵或按鈕移動角色</p>
                    <p>• 遇到紅色數字門時，輸入該數字的因數</p>
                    <p>• 輸入正確因數才能通過門</p>
                    <p>• 到達金色終點即可獲勝！</p>
                </div>

                <div class="control-section">
                    <h3>🎯 移動控制</h3>
                    <div class="movement-controls">
                        <div></div>
                        <button class="movement-btn" onclick="movePlayer('up')">↑</button>
                        <div></div>
                        <button class="movement-btn" onclick="movePlayer('left')">←</button>
                        <button class="movement-btn" onclick="movePlayer('down')">↓</button>
                        <button class="movement-btn" onclick="movePlayer('right')">→</button>
                    </div>
                    <button class="hint-button" onclick="showHint()">💡 顯示提示</button>
                    <div id="path-hint" class="path-hint" style="display: none;"></div>
                </div>

                <div class="control-section" id="door-section" style="display: none;">
                    <h3>🚪 數字門挑戰</h3>
                    <div class="door-question">
                        <div class="door-number" id="door-number">12</div>
                        <p>請輸入這個數字的因數：</p>
                        <input type="number" class="answer-input" id="factor-input" placeholder="輸入因數">
                        <button class="submit-btn" onclick="checkFactor()">確認答案</button>
                        <div class="feedback" id="feedback"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="victory-message" id="victory-message">
        <h2>🎉 恭喜通關！</h2>
        <p>你成功通過了所有因數門！</p>
        <p>用時: <span id="final-time"></span></p>
        <p>通過門數: <span id="final-doors"></span></p>
        <button class="restart-btn" onclick="restartGame()">再玩一次</button>
    </div>

    <script>
        // 遊戲狀態
        let gameState = {
            maze: [],
            playerPosition: { x: 0, y: 7 },
            goalPosition: { x: 9, y: 0 },
            doors: [],
            doorsPassed: 0,
            startTime: Date.now(),
            currentDoor: null,
            gameWon: false
        };

        // 迷宮地圖 (0=路徑, 1=牆壁, 2=門, 3=起點, 4=終點)
        // 精心設計確保必須通過所有門才能到達終點
        const mazeLayout = [
            [1,1,1,1,2,1,1,1,1,4], // 終點前有第四個門，必須通過
            [1,0,0,2,0,0,1,0,0,0], // 第一個門，通往上層路徑
            [1,0,1,1,1,0,1,0,1,1],
            [1,0,0,0,0,0,2,0,0,1], // 第二個門，中間關卡
            [1,1,1,0,1,1,1,1,0,1],
            [1,0,2,0,0,0,0,0,0,1], // 第三個門，下層關卡
            [1,0,1,1,1,1,1,0,1,1],
            [3,0,0,0,0,0,0,0,1,1]  // 起點，右側被阻擋
        ];

        // 門的配置 (x, y, 數字) - 確保必須按順序通過
        const doorConfig = [
            { x: 3, y: 1, number: 12 },  // 第一個門
            { x: 6, y: 3, number: 15 },  // 第二個門
            { x: 2, y: 5, number: 18 },  // 第三個門
            { x: 4, y: 0, number: 24 }   // 第四個門（終點前的最後一道門）
        ];

        // 檢查路徑是否可達（不通過門的情況下）
        function canReachWithoutDoors(start, end, maze) {
            const visited = new Set();
            const queue = [start];

            while (queue.length > 0) {
                const current = queue.shift();
                const key = `${current.x},${current.y}`;

                if (visited.has(key)) continue;
                visited.add(key);

                // 到達終點
                if (current.x === end.x && current.y === end.y) {
                    return true;
                }

                // 檢查四個方向
                const directions = [
                    { x: current.x + 1, y: current.y },
                    { x: current.x - 1, y: current.y },
                    { x: current.x, y: current.y + 1 },
                    { x: current.x, y: current.y - 1 }
                ];

                for (const next of directions) {
                    if (next.x < 0 || next.x >= maze[0].length ||
                        next.y < 0 || next.y >= maze.length) continue;

                    const cellType = maze[next.y][next.x];

                    // 只能通過路徑(0)，不能通過門(2)
                    if (cellType === 0) {
                        queue.push(next);
                    }
                }
            }

            return false;
        }

        // 檢查通過所有門後是否可達終點
        function isPathReachableWithAllDoors(start, end, maze, doors) {
            const visited = new Set();
            const queue = [start];

            while (queue.length > 0) {
                const current = queue.shift();
                const key = `${current.x},${current.y}`;

                if (visited.has(key)) continue;
                visited.add(key);

                // 到達終點
                if (current.x === end.x && current.y === end.y) {
                    return true;
                }

                // 檢查四個方向
                const directions = [
                    { x: current.x + 1, y: current.y },
                    { x: current.x - 1, y: current.y },
                    { x: current.x, y: current.y + 1 },
                    { x: current.x, y: current.y - 1 }
                ];

                for (const next of directions) {
                    if (next.x < 0 || next.x >= maze[0].length ||
                        next.y < 0 || next.y >= maze.length) continue;

                    const cellType = maze[next.y][next.x];

                    // 可以通過的格子：路徑(0)、門(2)、終點(4)
                    if (cellType === 0 || cellType === 2 || cellType === 4) {
                        queue.push(next);
                    }
                }
            }

            return false;
        }

        // 初始化遊戲
        function initGame() {
            createMaze();
            updateDisplay();
            startTimer();
            loadHighScore();

            // 檢查迷宮設計的合理性
            const canBypassDoors = canReachWithoutDoors(
                gameState.playerPosition,
                gameState.goalPosition,
                mazeLayout
            );

            const canReachWithDoors = isPathReachableWithAllDoors(
                gameState.playerPosition,
                gameState.goalPosition,
                mazeLayout,
                gameState.doors
            );

            console.log('不通過門能到達終點:', canBypassDoors ? '是' : '否');
            console.log('通過所有門能到達終點:', canReachWithDoors ? '是' : '否');

            if (canBypassDoors) {
                console.warn('警告：玩家可以繞過門直接到達終點！迷宮設計有問題！');
            }

            if (!canReachWithDoors) {
                console.warn('警告：即使通過所有門也無法到達終點！迷宮設計有問題！');
            }
        }

        // 創建迷宮
        function createMaze() {
            const grid = document.getElementById('maze-grid');
            grid.innerHTML = '';
            
            gameState.maze = mazeLayout.map(row => [...row]);
            gameState.doors = doorConfig.map(door => ({ ...door, passed: false }));
            
            for (let y = 0; y < mazeLayout.length; y++) {
                for (let x = 0; x < mazeLayout[y].length; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'maze-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    
                    const cellType = mazeLayout[y][x];
                    switch (cellType) {
                        case 0: // 路徑
                            cell.classList.add('path');
                            break;
                        case 1: // 牆壁
                            cell.classList.add('wall');
                            break;
                        case 2: // 門
                            cell.classList.add('door');
                            const door = doorConfig.find(d => d.x === x && d.y === y);
                            if (door) {
                                cell.textContent = door.number;
                            }
                            break;
                        case 3: // 起點
                            cell.classList.add('path');
                            break;
                        case 4: // 終點
                            cell.classList.add('goal');
                            cell.textContent = '🏁';
                            break;
                    }
                    
                    grid.appendChild(cell);
                }
            }
            
            updatePlayerPosition();
        }

        // 移動玩家
        function movePlayer(direction) {
            if (gameState.gameWon) return;
            
            const newPos = { ...gameState.playerPosition };
            
            switch (direction) {
                case 'up':
                    newPos.y = Math.max(0, newPos.y - 1);
                    break;
                case 'down':
                    newPos.y = Math.min(mazeLayout.length - 1, newPos.y + 1);
                    break;
                case 'left':
                    newPos.x = Math.max(0, newPos.x - 1);
                    break;
                case 'right':
                    newPos.x = Math.min(mazeLayout[0].length - 1, newPos.x + 1);
                    break;
            }
            
            // 檢查是否可以移動到新位置
            const cellType = mazeLayout[newPos.y][newPos.x];
            
            if (cellType === 1) { // 牆壁
                showFeedback('無法穿過牆壁！', 'incorrect');
                return;
            }
            
            if (cellType === 2) { // 門
                const door = gameState.doors.find(d => d.x === newPos.x && d.y === newPos.y);
                if (door && !door.passed) {
                    gameState.currentDoor = door;
                    showDoorChallenge();
                    return;
                } else if (door && door.passed) {
                    // 門已經通過，可以直接走過
                    console.log('通過已解鎖的門:', door);
                }
            }
            
            if (cellType === 4) { // 終點
                console.log('玩家到達終點！位置:', newPos);

                // 檢查是否通過了所有門
                const totalDoors = doorConfig.length;
                const passedDoors = gameState.doors.filter(door => door.passed).length;

                if (passedDoors < totalDoors) {
                    showFeedback(`你還需要通過 ${totalDoors - passedDoors} 個門才能到達終點！`, 'incorrect');
                    playSound('error');
                    return;
                }

                gameState.gameWon = true;
                showVictoryMessage();
                return;
            }
            
            // 移動玩家
            gameState.playerPosition = newPos;
            updatePlayerPosition();
            playSound('click');
        }

        // 顯示門的挑戰
        function showDoorChallenge() {
            document.getElementById('door-section').style.display = 'block';
            document.getElementById('door-number').textContent = gameState.currentDoor.number;
            document.getElementById('factor-input').value = '';
            document.getElementById('factor-input').focus();
            document.getElementById('feedback').innerHTML = '';
        }

        // 檢查因數答案
        function checkFactor() {
            const input = document.getElementById('factor-input');
            const answer = parseInt(input.value);
            const targetNumber = gameState.currentDoor.number;
            
            if (isNaN(answer) || answer <= 0) {
                showFeedback('請輸入有效的正整數！', 'incorrect');
                return;
            }
            
            if (answer >= targetNumber) {
                showFeedback('因數必須小於原數字！', 'incorrect');
                return;
            }
            
            if (targetNumber % answer === 0) {
                // 正確答案
                showFeedback('正確！門已打開！', 'correct');
                gameState.currentDoor.passed = true;
                gameState.doorsPassed++;
                
                setTimeout(() => {
                    document.getElementById('door-section').style.display = 'none';
                    updateDisplay();
                    playSound('success');

                    // 門解鎖後，自動移動玩家到門的位置
                    gameState.playerPosition = { x: gameState.currentDoor.x, y: gameState.currentDoor.y };
                    updatePlayerPosition();
                }, 1500);
            } else {
                showFeedback('錯誤！這個數字不是因數！', 'incorrect');
                playSound('error');
            }
        }

        // 顯示反饋訊息
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.textContent = message;
            feedback.className = `feedback ${type}`;
        }

        // 更新玩家位置顯示
        function updatePlayerPosition() {
            const cells = document.querySelectorAll('.maze-cell');
            cells.forEach(cell => cell.classList.remove('player'));
            
            const playerCell = document.querySelector(`[data-x="${gameState.playerPosition.x}"][data-y="${gameState.playerPosition.y}"]`);
            if (playerCell) {
                playerCell.classList.add('player');
                playerCell.textContent = '🚶‍♂️';
            }
        }

        // 更新顯示
        function updateDisplay() {
            document.getElementById('doors-passed').textContent = gameState.doorsPassed;
            
            // 更新門的狀態
            gameState.doors.forEach(door => {
                const cell = document.querySelector(`[data-x="${door.x}"][data-y="${door.y}"]`);
                if (cell && door.passed) {
                    cell.style.opacity = '0.5';
                    cell.textContent = '✓';
                }
            });
        }

        // 開始計時器
        function startTimer() {
            setInterval(() => {
                if (!gameState.gameWon) {
                    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
                    const minutes = Math.floor(elapsed / 60);
                    const seconds = elapsed % 60;
                    document.getElementById('game-time').textContent = 
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }

        // 顯示勝利訊息
        function showVictoryMessage() {
            const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            document.getElementById('final-time').textContent = timeString;
            document.getElementById('final-doors').textContent = gameState.doorsPassed;
            
            // 計算分數
            const score = Math.max(0, 1000 - elapsed * 10 + gameState.doorsPassed * 100);
            setHighScore('maze-game', score);
            
            document.getElementById('victory-message').classList.add('show');
            celebrateConfetti(100);
            playSound('success');
        }

        // 顯示提示
        function showHint() {
            const hintDiv = document.getElementById('path-hint');

            if (hintDiv.style.display === 'none') {
                let hintText = '';

                // 檢查玩家當前位置
                const playerPos = gameState.playerPosition;

                // 檢查是否有未通過的門
                const unpassedDoors = gameState.doors.filter(door => !door.passed);

                if (unpassedDoors.length > 0) {
                    const nearestDoor = unpassedDoors[0];
                    hintText = `🚪 你需要先通過門才能到達終點！<br>`;
                    hintText += `最近的門在位置 (${nearestDoor.x + 1}, ${nearestDoor.y + 1})，數字是 ${nearestDoor.number}。<br>`;
                    hintText += `${nearestDoor.number} 的因數包括：${getFactors(nearestDoor.number).join(', ')}`;
                } else {
                    // 所有門都通過了，指向終點
                    const goalPos = gameState.goalPosition;
                    if (playerPos.x < goalPos.x) {
                        hintText = '🎯 向右移動接近終點！';
                    } else if (playerPos.x > goalPos.x) {
                        hintText = '🎯 向左移動接近終點！';
                    } else if (playerPos.y < goalPos.y) {
                        hintText = '🎯 向下移動接近終點！';
                    } else if (playerPos.y > goalPos.y) {
                        hintText = '🎯 向上移動接近終點！';
                    } else {
                        hintText = '🎉 你已經在終點了！';
                    }
                }

                hintDiv.innerHTML = hintText;
                hintDiv.style.display = 'block';

                // 5秒後自動隱藏提示
                setTimeout(() => {
                    hintDiv.style.display = 'none';
                }, 5000);
            } else {
                hintDiv.style.display = 'none';
            }
        }

        // 獲取數字的所有因數
        function getFactors(num) {
            const factors = [];
            for (let i = 1; i < num; i++) {
                if (num % i === 0) {
                    factors.push(i);
                }
            }
            return factors;
        }

        // 重新開始遊戲
        function restartGame() {
            document.getElementById('victory-message').classList.remove('show');
            gameState = {
                maze: [],
                playerPosition: { x: 0, y: 7 },
                goalPosition: { x: 9, y: 0 },
                doors: [],
                doorsPassed: 0,
                startTime: Date.now(),
                currentDoor: null,
                gameWon: false
            };
            initGame();
        }

        // 載入最高分
        function loadHighScore() {
            const highScore = getHighScore('maze-game');
            document.getElementById('high-score').textContent = highScore;
        }

        // 簡化的工具函數
        function playSound(type) {
            // 簡化版音效，避免模組導入問題
            try {
                const audio = new Audio();
                audio.volume = 0.3;
                audio.play().catch(() => {});
            } catch (e) {}
        }

        function getHighScore(key) {
            try {
                return Number(localStorage.getItem(key) || 0);
            } catch (_) {
                return 0;
            }
        }

        function setHighScore(key, value) {
            try {
                const current = getHighScore(key);
                if (value > current) localStorage.setItem(key, String(value));
            } catch (_) {}
        }

        function celebrateConfetti(pieces = 80) {
            // 簡化版彩帶效果
            const colors = ['#ffd06b', '#8b91ff', '#ff91a4', '#43e97b', '#4facfe'];
            for (let i = 0; i < pieces; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.borderRadius = '50%';
                confetti.style.pointerEvents = 'none';
                confetti.style.zIndex = '9999';
                confetti.style.animation = `fall ${Math.random() * 2 + 2}s linear forwards`;
                
                document.body.appendChild(confetti);
                
                setTimeout(() => confetti.remove(), 4000);
            }
        }

        // 添加CSS動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 鍵盤控制
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    e.preventDefault();
                    movePlayer('up');
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    e.preventDefault();
                    movePlayer('down');
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    e.preventDefault();
                    movePlayer('left');
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    e.preventDefault();
                    movePlayer('right');
                    break;
                case 'Enter':
                    if (document.getElementById('door-section').style.display !== 'none') {
                        e.preventDefault();
                        checkFactor();
                    }
                    break;
            }
        });

        // 初始化遊戲
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
