<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數獵人遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .target-number {
            font-size: 3em;
            color: #e74c3c;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 3px solid #e74c3c;
        }
        .timer {
            font-size: 1.5em;
            color: #f39c12;
            margin: 20px 0;
        }
        .input-section {
            margin: 30px 0;
        }
        input[type="number"] {
            font-size: 1.2em;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 10px;
            width: 80px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        button:hover {
            background: #2980b9;
        }
        .factors-display {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
            min-height: 50px;
        }
        .factor-item {
            background: #2ecc71;
            color: white;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 20px;
            font-size: 1.1em;
            animation: popIn 0.3s ease;
        }
        @keyframes popIn {
            0% { transform: scale(0); }
            100% { transform: scale(1); }
        }
        .score {
            font-size: 1.3em;
            color: #27ae60;
            margin: 20px 0;
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #95a5a6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            text-decoration: none;
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🔍 因數獵人遊戲</h1>
        <div class="target-number" id="targetNumber">12</div>
        <div class="timer" id="timer">時間: 30秒</div>
        <div class="score" id="score">得分: 0</div>
        
        <div class="input-section">
            <input type="number" id="factorInput" placeholder="輸入因數" min="1">
            <button onclick="addFactor()">添加因數</button>
            <button onclick="checkAnswer()">檢查答案</button>
            <button onclick="newGame()">新遊戲</button>
        </div>
        
        <div class="factors-display" id="factorsDisplay"></div>
        <div class="message" id="message"></div>
    </div>

    <script type="module">
        import { playSound, celebrateConfetti, getHighScore, setHighScore } from './game-utils.js';
        let targetNumber = 12;
        let timeLeft = 30;
        let score = 0;
        let foundFactors = [];
        let timer;
        let gameActive = true;
        const HS_KEY = 'hs-factor-hunter';

        function generateRandomNumber() {
            const numbers = [12, 18, 24, 30, 36, 42, 48, 56, 60, 72, 84, 96];
            return numbers[Math.floor(Math.random() * numbers.length)];
        }

        function startTimer() {
            timer = setInterval(() => {
                timeLeft--;
                document.getElementById('timer').textContent = `時間: ${timeLeft}秒`;
                
                if (timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function addFactor() {
            if (!gameActive) return;
            
            const input = document.getElementById('factorInput');
            const factor = parseInt(input.value);
            
            if (isNaN(factor) || factor < 1) {
                showMessage('請輸入有效的正整數！', 'error');
                return;
            }
            
            if (foundFactors.includes(factor)) {
                showMessage('這個因數已經添加過了！', 'error');
                return;
            }
            
            if (targetNumber % factor === 0) {
                foundFactors.push(factor);
                displayFactors();
                input.value = '';
                showMessage('正確！', 'success');
            } else {
                showMessage('這不是因數！', 'error');
            }
        }

        function displayFactors() {
            const display = document.getElementById('factorsDisplay');
            display.innerHTML = '';
            foundFactors.forEach(factor => {
                const div = document.createElement('div');
                div.className = 'factor-item';
                div.textContent = factor;
                display.appendChild(div);
            });
        }

        function checkAnswer() {
            if (!gameActive) return;
            
            const allFactors = getAllFactors(targetNumber);
            const correctFactors = allFactors.filter(f => f !== 1 && f !== targetNumber);
            
            if (foundFactors.length === correctFactors.length && 
                foundFactors.every(f => correctFactors.includes(f))) {
                score += 10;
                showMessage('恭喜！找到所有因數！', 'success');
                playSound('success');
                celebrateConfetti(80);
                setTimeout(newGame, 2000);
            } else {
                showMessage(`還差 ${correctFactors.length - foundFactors.length} 個因數！`, 'error');
                playSound('error');
            }
        }

        function getAllFactors(num) {
            const factors = [];
            for (let i = 1; i <= num; i++) {
                if (num % i === 0) {
                    factors.push(i);
                }
            }
            return factors;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            targetNumber = generateRandomNumber();
            timeLeft = 30;
            foundFactors = [];
            score = 0;
            
            document.getElementById('targetNumber').textContent = targetNumber;
            document.getElementById('timer').textContent = `時間: ${timeLeft}秒`;
            document.getElementById('score').textContent = `得分: ${score}`;
            document.getElementById('factorsDisplay').innerHTML = '';
            document.getElementById('message').textContent = '';
            document.getElementById('factorInput').value = '';
            
            const high = getHighScore(HS_KEY);
            document.getElementById('score').innerHTML = `得分: ${score} <span class="high-score">(最高分：${high})</span>`;
            clearInterval(timer);
            startTimer();
        }

        function endGame() {
            gameActive = false;
            clearInterval(timer);
            setHighScore(HS_KEY, score);
            showMessage('時間到！遊戲結束！', 'error');
        }

        // 開始遊戲
        newGame();
    </script>
</body>
</html>
