<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>因數獵人遊戲</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(139, 145, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 145, 164, 0.1) 0%, transparent 50%);
            animation: backgroundFloat 15s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }

        .game-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            text-align: center;
            max-width: 700px;
            width: 100%;
            position: relative;
            animation: containerSlideIn 0.8s ease-out;
        }

        @keyframes containerSlideIn {
            from { opacity: 0; transform: translateY(30px) scale(0.95); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }

        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 30px;
            font-size: 2.8em;
            font-weight: 900;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            animation: titlePulse 2s ease-in-out infinite alternate;
        }

        @keyframes titlePulse {
            0% { filter: brightness(1); }
            100% { filter: brightness(1.1); }
        }
        .target-number {
            font-size: 4em;
            font-weight: 900;
            margin: 25px 0;
            padding: 25px;
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 3px solid #ef4444;
            border-radius: 20px;
            color: #dc2626;
            text-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
            position: relative;
            animation: targetGlow 2s ease-in-out infinite alternate;
        }

        @keyframes targetGlow {
            0% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.3); }
            100% { box-shadow: 0 0 30px rgba(239, 68, 68, 0.5); }
        }

        .target-number::before {
            content: '🎯';
            position: absolute;
            top: -15px;
            right: -15px;
            font-size: 0.4em;
            animation: bounce 2s ease-in-out infinite;
        }

        .timer {
            font-size: 1.8em;
            font-weight: 700;
            color: #f59e0b;
            margin: 25px 0;
            padding: 15px 25px;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 15px;
            border: 2px solid #f59e0b;
            display: inline-block;
            min-width: 200px;
            text-shadow: 0 1px 2px rgba(245, 158, 11, 0.3);
        }

        .timer.warning {
            animation: timerWarning 1s ease-in-out infinite;
            color: #dc2626;
            border-color: #dc2626;
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
        }

        @keyframes timerWarning {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .input-section {
            margin: 35px 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            align-items: center;
        }
        input[type="number"] {
            font-size: 1.4em;
            padding: 15px 20px;
            border: 3px solid #e5e7eb;
            border-radius: 15px;
            width: 120px;
            text-align: center;
            font-weight: 700;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        input[type="number"]:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
            transform: translateY(-2px);
        }

        button {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            font-size: 1.1em;
            font-weight: 700;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
            transition: left 0.5s ease;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .btn-success:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-warning:hover {
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
        }
        .factors-display {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
            min-height: 80px;
            padding: 20px;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-radius: 20px;
            border: 2px dashed #22c55e;
            position: relative;
        }

        .factors-display::before {
            content: '已找到的因數 🎯';
            position: absolute;
            top: -12px;
            left: 20px;
            background: white;
            padding: 0 10px;
            font-size: 0.9em;
            font-weight: 700;
            color: #16a34a;
        }

        .factor-item {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
            animation: factorPopIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .factor-item:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(34, 197, 94, 0.4);
        }

        .factor-item::after {
            content: '✓';
            position: absolute;
            top: -5px;
            right: -5px;
            background: #fbbf24;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
            font-weight: 900;
        }

        @keyframes factorPopIn {
            0% {
                transform: scale(0) rotate(-180deg);
                opacity: 0;
            }
            50% {
                transform: scale(1.2) rotate(-90deg);
                opacity: 0.8;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }

        .score {
            font-size: 1.6em;
            font-weight: 900;
            color: #059669;
            margin: 25px 0;
            padding: 15px 25px;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-radius: 15px;
            border: 2px solid #10b981;
            display: inline-block;
            min-width: 250px;
            text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2);
        }
        .message {
            font-size: 1.2em;
            margin: 15px 0;
            padding: 10px;
            border-radius: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            cursor: pointer;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(148, 163, 184, 0.3);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(148, 163, 184, 0.4);
        }

        /* Difficulty selector */
        .difficulty-selector {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-radius: 15px;
            border: 2px solid #3b82f6;
        }

        .difficulty-selector h3 {
            margin: 0 0 15px 0;
            color: #1d4ed8;
            font-size: 1.2em;
        }

        .difficulty-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .difficulty-btn {
            padding: 10px 20px;
            border: 2px solid #3b82f6;
            background: white;
            color: #3b82f6;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .difficulty-btn.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .difficulty-btn:hover {
            transform: translateY(-1px);
        }

        /* Achievement system */
        .achievements {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
            border-radius: 15px;
            border: 2px solid #f59e0b;
        }

        .achievements h3 {
            margin: 0 0 15px 0;
            color: #d97706;
            font-size: 1.2em;
        }

        .achievement-list {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .achievement {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .achievement.unlocked {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
        }

        .achievement.locked {
            background: #f3f4f6;
            color: #9ca3af;
            border: 2px dashed #d1d5db;
        }

        /* Progress bar */
        .progress-container {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
        }

        .progress-label {
            font-size: 0.9em;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 6px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body class="cute-container">
    <a href="index.html" class="back-btn">← 返回主選單</a>
    
    <div class="game-container">
        <h1>🔍 因數獵人遊戲</h1>

        <!-- Difficulty Selector -->
        <div class="difficulty-selector">
            <h3>🎯 選擇難度</h3>
            <div class="difficulty-buttons">
                <button class="difficulty-btn active" data-difficulty="easy">簡單</button>
                <button class="difficulty-btn" data-difficulty="medium">中等</button>
                <button class="difficulty-btn" data-difficulty="hard">困難</button>
                <button class="difficulty-btn" data-difficulty="expert">專家</button>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-label">找到因數進度</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <div class="target-number" id="targetNumber">12</div>
        <div class="timer" id="timer">時間: 30秒</div>
        <div class="score" id="score">得分: 0</div>

        <div class="input-section">
            <input type="number" id="factorInput" placeholder="輸入因數" min="1">
            <button onclick="addFactor()" class="btn-success">添加因數</button>
            <button onclick="checkAnswer()" class="btn-warning">檢查答案</button>
            <button onclick="newGame()">新遊戲</button>
        </div>

        <div class="factors-display" id="factorsDisplay"></div>

        <!-- Achievements -->
        <div class="achievements">
            <h3>🏆 成就系統</h3>
            <div class="achievement-list" id="achievementList"></div>
        </div>

        <div class="message" id="message"></div>
    </div>

    <script type="module">
        import {
            playSound,
            celebrateConfetti,
            getHighScore,
            setHighScore,
            getGameStats,
            updateGameStats,
            createFloatingText,
            createParticleEffect,
            animateElement,
            shakeScreen
        } from './game-utils.js';

        let targetNumber = 12;
        let timeLeft = 30;
        let score = 0;
        let foundFactors = [];
        let timer;
        let gameActive = true;
        let currentDifficulty = 'easy';
        let streak = 0;
        let totalFactorsFound = 0;

        const HS_KEY = 'hs-factor-hunter';
        const STATS_KEY = 'stats-factor-hunter';

        // Difficulty settings
        const DIFFICULTY_SETTINGS = {
            easy: {
                timeLimit: 45,
                numbers: [12, 18, 24, 30, 36],
                scoreMultiplier: 1,
                name: '簡單'
            },
            medium: {
                timeLimit: 35,
                numbers: [42, 48, 56, 60, 72, 84],
                scoreMultiplier: 1.5,
                name: '中等'
            },
            hard: {
                timeLimit: 25,
                numbers: [96, 108, 120, 144, 168, 180],
                scoreMultiplier: 2,
                name: '困難'
            },
            expert: {
                timeLimit: 20,
                numbers: [210, 240, 270, 300, 360, 420],
                scoreMultiplier: 3,
                name: '專家'
            }
        };

        // Achievement definitions
        const ACHIEVEMENTS = [
            { id: 'first_factor', name: '初次發現', desc: '找到第一個因數', icon: '🎯' },
            { id: 'speed_demon', name: '速度惡魔', desc: '10秒內完成一輪', icon: '⚡' },
            { id: 'perfect_round', name: '完美一輪', desc: '一次找到所有因數', icon: '💎' },
            { id: 'streak_5', name: '連勝達人', desc: '連續成功5輪', icon: '🔥' },
            { id: 'factor_master', name: '因數大師', desc: '總共找到100個因數', icon: '👑' },
            { id: 'time_saver', name: '時間管理', desc: '剩餘時間超過50%完成', icon: '⏰' },
            { id: 'expert_level', name: '專家挑戰', desc: '在專家難度下獲勝', icon: '🏆' }
        ];

        let unlockedAchievements = JSON.parse(localStorage.getItem('achievements-factor-hunter') || '[]');

        function generateRandomNumber() {
            const settings = DIFFICULTY_SETTINGS[currentDifficulty];
            return settings.numbers[Math.floor(Math.random() * settings.numbers.length)];
        }

        function initializeDifficultySelector() {
            const buttons = document.querySelectorAll('.difficulty-btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', () => {
                    buttons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    currentDifficulty = btn.dataset.difficulty;
                    playSound('click');
                    newGame();
                });
            });
        }

        function updateProgress() {
            const allFactors = getAllFactors(targetNumber);
            const correctFactors = allFactors.filter(f => f !== 1 && f !== targetNumber);
            const progress = (foundFactors.length / correctFactors.length) * 100;

            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = progress + '%';

            if (progress === 100) {
                animateElement(progressFill.parentElement, 'pulse');
            }
        }

        function unlockAchievement(achievementId) {
            if (!unlockedAchievements.includes(achievementId)) {
                unlockedAchievements.push(achievementId);
                localStorage.setItem('achievements-factor-hunter', JSON.stringify(unlockedAchievements));

                const achievement = ACHIEVEMENTS.find(a => a.id === achievementId);
                if (achievement) {
                    showAchievementNotification(achievement);
                    playSound('level_up');
                    celebrateConfetti(30, 'burst');
                }
            }
        }

        function showAchievementNotification(achievement) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                font-size: 1.2em;
                font-weight: 700;
                box-shadow: 0 10px 30px rgba(251, 191, 36, 0.4);
                z-index: 10000;
                animation: achievementPop 3s ease-out forwards;
            `;
            notification.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 10px;">${achievement.icon}</div>
                    <div>成就解鎖！</div>
                    <div style="font-size: 0.9em; margin-top: 5px;">${achievement.name}</div>
                </div>
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);

            // Add achievement animation keyframes
            if (!document.querySelector('#achievement-keyframes')) {
                const style = document.createElement('style');
                style.id = 'achievement-keyframes';
                style.textContent = `
                    @keyframes achievementPop {
                        0% { transform: translate(-50%, -50%) scale(0) rotate(-180deg); opacity: 0; }
                        20% { transform: translate(-50%, -50%) scale(1.2) rotate(-10deg); opacity: 1; }
                        100% { transform: translate(-50%, -50%) scale(1) rotate(0deg); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        function updateAchievementDisplay() {
            const achievementList = document.getElementById('achievementList');
            achievementList.innerHTML = '';

            ACHIEVEMENTS.forEach(achievement => {
                const div = document.createElement('div');
                div.className = `achievement ${unlockedAchievements.includes(achievement.id) ? 'unlocked' : 'locked'}`;
                div.innerHTML = `${achievement.icon} ${achievement.name}`;
                div.title = achievement.desc;
                achievementList.appendChild(div);
            });
        }

        function startTimer() {
            const timerElement = document.getElementById('timer');
            timer = setInterval(() => {
                timeLeft--;
                timerElement.textContent = `時間: ${timeLeft}秒`;

                // Add warning animation when time is low
                if (timeLeft <= 10) {
                    timerElement.classList.add('warning');
                    if (timeLeft <= 5) {
                        playSound('tick', 0.2);
                    }
                } else {
                    timerElement.classList.remove('warning');
                }

                if (timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function addFactor() {
            if (!gameActive) return;

            const input = document.getElementById('factorInput');
            const factor = parseInt(input.value);

            if (isNaN(factor) || factor < 1) {
                showMessage('請輸入有效的正整數！', 'error');
                playSound('error');
                animateElement(input, 'shake');
                return;
            }

            if (foundFactors.includes(factor)) {
                showMessage('這個因數已經添加過了！', 'error');
                playSound('error');
                animateElement(input, 'shake');
                return;
            }

            if (targetNumber % factor === 0) {
                foundFactors.push(factor);
                totalFactorsFound++;
                displayFactors();
                updateProgress();
                input.value = '';

                // Create floating text effect
                const rect = input.getBoundingClientRect();
                createFloatingText(`+${Math.round(10 * DIFFICULTY_SETTINGS[currentDifficulty].scoreMultiplier)}`,
                    rect.left + rect.width/2, rect.top, '#22c55e');

                // Particle effect
                createParticleEffect(rect.left + rect.width/2, rect.top + rect.height/2, '#22c55e', 8);

                showMessage('正確！', 'success');
                playSound('collect');

                // Check for first factor achievement
                if (totalFactorsFound === 1) {
                    unlockAchievement('first_factor');
                }

                // Check for factor master achievement
                if (totalFactorsFound >= 100) {
                    unlockAchievement('factor_master');
                }

            } else {
                showMessage('這不是因數！', 'error');
                playSound('error');
                animateElement(input, 'shake');
                shakeScreen(5, 300);
            }
        }

        function displayFactors() {
            const display = document.getElementById('factorsDisplay');
            display.innerHTML = '';
            foundFactors.forEach(factor => {
                const div = document.createElement('div');
                div.className = 'factor-item';
                div.textContent = factor;
                display.appendChild(div);
            });
        }

        function checkAnswer() {
            if (!gameActive) return;

            const allFactors = getAllFactors(targetNumber);
            const correctFactors = allFactors.filter(f => f !== 1 && f !== targetNumber);

            if (foundFactors.length === correctFactors.length &&
                foundFactors.every(f => correctFactors.includes(f))) {

                const settings = DIFFICULTY_SETTINGS[currentDifficulty];
                const baseScore = 100;
                const timeBonus = Math.max(0, timeLeft * 2);
                const difficultyBonus = Math.round(baseScore * (settings.scoreMultiplier - 1));
                const totalRoundScore = Math.round((baseScore + timeBonus + difficultyBonus) * settings.scoreMultiplier);

                score += totalRoundScore;
                streak++;

                // Create floating score text
                const container = document.querySelector('.game-container');
                const rect = container.getBoundingClientRect();
                createFloatingText(`+${totalRoundScore}`,
                    rect.left + rect.width/2, rect.top + rect.height/2, '#fbbf24');

                showMessage(`恭喜！找到所有因數！獲得 ${totalRoundScore} 分！`, 'success');
                playSound('success');
                celebrateConfetti(100, 'default');

                // Check achievements
                const elapsedTime = DIFFICULTY_SETTINGS[currentDifficulty].timeLimit - timeLeft;
                if (elapsedTime <= 10) {
                    unlockAchievement('speed_demon');
                }

                if (foundFactors.length === correctFactors.length && foundFactors.length > 0) {
                    unlockAchievement('perfect_round');
                }

                if (streak >= 5) {
                    unlockAchievement('streak_5');
                }

                if (timeLeft > DIFFICULTY_SETTINGS[currentDifficulty].timeLimit * 0.5) {
                    unlockAchievement('time_saver');
                }

                if (currentDifficulty === 'expert') {
                    unlockAchievement('expert_level');
                }

                updateGameStats(STATS_KEY, totalRoundScore, elapsedTime);
                setTimeout(newGame, 3000);

            } else {
                const missing = correctFactors.length - foundFactors.length;
                showMessage(`還差 ${missing} 個因數！提示：試試 ${correctFactors.find(f => !foundFactors.includes(f)) || '?'}`, 'error');
                playSound('error');
                animateElement(document.querySelector('.factors-display'), 'shake');
            }
        }

        function getAllFactors(num) {
            const factors = [];
            for (let i = 1; i <= num; i++) {
                if (num % i === 0) {
                    factors.push(i);
                }
            }
            return factors;
        }

        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
        }

        function newGame() {
            gameActive = true;
            targetNumber = generateRandomNumber();
            const settings = DIFFICULTY_SETTINGS[currentDifficulty];
            timeLeft = settings.timeLimit;
            foundFactors = [];

            // Don't reset score for continuous play
            if (score === 0) {
                const stats = getGameStats(STATS_KEY);
                if (stats.gamesPlayed === 0) {
                    score = 0; // First time playing
                }
            }

            document.getElementById('targetNumber').textContent = targetNumber;
            document.getElementById('timer').textContent = `時間: ${timeLeft}秒`;
            document.getElementById('timer').classList.remove('warning');
            document.getElementById('factorsDisplay').innerHTML = '';
            document.getElementById('message').textContent = '';
            document.getElementById('factorInput').value = '';

            updateProgress();
            updateScoreDisplay();
            updateAchievementDisplay();

            clearInterval(timer);
            startTimer();

            // Add entrance animation
            animateElement(document.getElementById('targetNumber'), 'pop');
        }

        function updateScoreDisplay() {
            const high = getHighScore(HS_KEY);
            const stats = getGameStats(STATS_KEY);
            const isNewHigh = setHighScore(HS_KEY, score);

            document.getElementById('score').innerHTML = `
                得分: ${score}
                <span class="high-score">(最高分：${Math.max(high, score)})</span>
                <br>
                <small style="color: #64748b;">
                    難度: ${DIFFICULTY_SETTINGS[currentDifficulty].name} |
                    連勝: ${streak} |
                    總遊戲: ${stats.gamesPlayed}
                </small>
            `;

            if (isNewHigh && score > 0) {
                createFloatingText('新紀錄！',
                    window.innerWidth/2, window.innerHeight/3, '#fbbf24');
                celebrateConfetti(50, 'burst');
            }
        }

        function endGame() {
            gameActive = false;
            clearInterval(timer);
            streak = 0; // Reset streak on game over

            const finalScore = score;
            updateGameStats(STATS_KEY, 0, 0); // Record game played
            setHighScore(HS_KEY, finalScore);

            showMessage(`時間到！遊戲結束！最終得分：${finalScore}`, 'error');
            playSound('error');
            shakeScreen(8, 500);

            // Show restart option
            setTimeout(() => {
                if (confirm('遊戲結束！要重新開始嗎？')) {
                    score = 0;
                    newGame();
                }
            }, 2000);
        }

        // Add keyboard support
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const input = document.getElementById('factorInput');
                if (document.activeElement === input && input.value.trim()) {
                    addFactor();
                }
            } else if (e.key === ' ' && e.ctrlKey) {
                e.preventDefault();
                checkAnswer();
            } else if (e.key === 'Escape') {
                newGame();
            }
        });

        // Initialize game
        window.addEventListener('load', () => {
            initializeDifficultySelector();
            updateAchievementDisplay();
            newGame();

            // Focus input for better UX
            document.getElementById('factorInput').focus();
        });
    </script>
</body>
</html>
